#!/usr/bin/env node

import { spawn } from "child_process";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Wrapper script that sets up custom CA and runs the actual start script
const env = {
  ...process.env,
  NODE_EXTRA_CA_CERTS: path.resolve(__dirname, "ca.pem"),
};

const startScript = path.resolve(__dirname, "_start.js");
const startProcess = spawn("node", [startScript, ...process.argv.slice(2)], {
  env: env,
  stdio: "inherit",
});

startProcess.on("close", (code) => {
  process.exit(code);
});

startProcess.on("error", (error) => {
  console.error("Failed to start:", error);
  process.exit(1);
});