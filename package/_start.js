#!/usr/bin/env node

import fs from "fs";
import path from "path";
import os from "os";
import express from "express";
import open from "open";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import { spawn } from "child_process";
import { fileURLToPath } from "url";
import readline from "readline";
import { createServer } from "http";
import chalk from "chalk";

process.env.CLAUDE_CODE_DISABLE_NONESSENTIAL_TRAFFIC = "1";
process.env.DISABLE_TELEMETRY = "1";
process.env.DISABLE_AUTOUPDATER = 1;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

(function () {
  const userArgs = process.argv.slice(2); // ignore "node" and this script's path
  const CANONICAL_RELAY = process.env.CLAUDE_CODE_HOST || "gaccode.com";

  // Check for --pick-relay flag
  const pickRelayIndex = userArgs.indexOf("--pick-relay");
  if (pickRelayIndex !== -1) {
    // Check if a specific relay is provided
    const nextArg = userArgs[pickRelayIndex + 1];
    if (nextArg && !nextArg.startsWith("--")) {
      // Specific relay provided, write it directly to config
      const relayHost = nextArg;
      const relayData = { host: relayHost };

      try {
        const configDir = path.join(os.homedir(), ".claudecode");
        if (!fs.existsSync(configDir)) {
          fs.mkdirSync(configDir, { recursive: true });
        }

        const relayFile = path.join(configDir, "relay");
        fs.writeFileSync(relayFile, JSON.stringify(relayData, null, 2));

        console.log(chalk.green(`✅ Relay set to: ${relayHost}`));
        process.exit(0);
      } catch (error) {
        console.error(chalk.red("❌ Failed to set relay:"), error.message);
        process.exit(1);
      }
    } else {
      // No specific relay provided, launch relay selector
      const relaySelectorPath = path.resolve(__dirname, "relay-selector.js");
      const relayProcess = spawn(
        "node",
        [relaySelectorPath, "--domain", CANONICAL_RELAY],
        {
          stdio: "inherit", // This will pipe the output to the current process
        },
      );

      relayProcess.on("close", (code) => {
        process.exit(code);
      });

      relayProcess.on("error", (error) => {
        console.error("Failed to launch relay selector:", error);
        process.exit(1);
      });
    }
    return;
  }

  // Check for --logout flag
  if (userArgs.includes("--logout")) {
    const configFile = path.join(os.homedir(), ".claudecode", "config");
    try {
      if (fs.existsSync(configFile)) {
        fs.unlinkSync(configFile);
      }
      console.log(chalk.green("✅ Logout successful"));
    } catch (error) {
      console.error(chalk.red("❌ Failed to logout:"), error.message);
      process.exit(1);
    }
    process.exit(0);
  }

  // Determine which relay to use
  let currentRelay = CANONICAL_RELAY;
  let isCanonical = true;

  try {
    const relayFile = path.join(os.homedir(), ".claudecode", "relay");
    if (fs.existsSync(relayFile)) {
      const relayData = JSON.parse(fs.readFileSync(relayFile, "utf-8"));
      if (relayData.host) {
        currentRelay = relayData.host;
        isCanonical = relayData.host === CANONICAL_RELAY;
      }
    }
  } catch (error) {
    // If relay file is invalid, fall back to canonical
    currentRelay = CANONICAL_RELAY;
    isCanonical = true;
  }

  // Display welcome message with relay info
  console.log("\n" + chalk.bold.cyan("🌟 Welcome to Claude Code!"));
  if (isCanonical) {
    console.log(
      chalk.green("🔗 You are using the ") +
        chalk.bold.green("canonical relay"),
    );
  } else {
    console.log(
      chalk.yellow("🔗 You are using custom relay: ") +
        chalk.bold.yellow(currentRelay),
    );
  }
  console.log(
    chalk.gray("💡 If the relay doesn't work, you can run ") +
      chalk.bold.cyan("`claude --pick-relay`") +
      "\n",
  );
  /**
   * OAuth Flow
   * -----------------------------------
   * 1. Checks if there's already a token in ~/.claudecode/config
   * 2. If no token, starts a local server, opens browser, waits for callback
   * 3. Verifies token, saves it, and calls `onAuthenticated(tokenData)`
   */

  // ──────────────────────────────────────────────────────────
  // CONFIGURATION
  // ──────────────────────────────────────────────────────────
  const SERVER_URL = `https://${currentRelay}`;
  const CLIENT_ID = process.env.CLIENT_ID || "c35a52681f1fa87a6a11f69d26990326";
  const CLIENT_SECRET =
    process.env.CLIENT_SECRET ||
    "2935467f5e0e1d383a51a467c9680091dc29015291245dbb6b440adcaf9e1011";

  // Callback server settings
  const CALLBACK_HOST = process.env.CALLBACK_HOST || "localhost";
  const CALLBACK_PATH = "/oauth/callback";

  // Paths for storing config/token
  const CONFIG_DIR = path.join(os.homedir(), ".claudecode");
  const CONFIG_FILE = path.join(CONFIG_DIR, "config");

  // ──────────────────────────────────────────────────────────
  // UTILS
  // ──────────────────────────────────────────────────────────

  /** Find an available port starting from a high port number */
  function findAvailablePort() {
    return new Promise((resolve, reject) => {
      const server = createServer();
      server.listen(0, CALLBACK_HOST, () => {
        const port = server.address().port;
        server.close(() => resolve(port));
      });
      server.on("error", reject);
    });
  }

  /** Fancy HTML boilerplate */
  function renderHtml({ title, heading, bodyHtml }) {
    return `
    <!doctype html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>${title}</title>
      <style>
        body { font-family: "Segoe UI", Arial, sans-serif; background:#f7f8fa; color:#333;
               display:flex; flex-direction:column; align-items:center; justify-content:center; height:100vh; margin:0 }
        .card { background:white; padding:2.5rem 3rem; border-radius:12px; box-shadow:0 6px 18px rgba(0,0,0,.1); max-width:480px; }
        h1   { margin-top:0; color:#2c3e50; font-size:1.75rem }
        p    { line-height:1.5 }
        code { background:#f1f1f1; padding:0.2rem 0.4rem; border-radius:4px; font-family:Consolas,monospace; }
        .ok  { color:#27ae60 }
        .err { color:#e74c3c }
        footer{ margin-top:1.5rem; font-size:.85rem; color:#888 }
      </style>
    </head>
    <body>
      <div class="card">
        <h1>${heading}</h1>
        ${bodyHtml}
        <footer>ClaudeCode OAuth Client</footer>
      </div>
    </body>
    </html>
  `;
  }

  /** Read token (if any) */
  function readToken() {
    try {
      if (!fs.existsSync(CONFIG_FILE)) return null;
      const raw = fs.readFileSync(CONFIG_FILE, "utf-8");
      const data = JSON.parse(raw);

      if (!data.token) return null;
      return data;
    } catch (err) {
      console.error("Error reading token:", err.message);
      return null;
    }
  }

  /**
   * Save the token to ~/.claudecode/config
   */
  function saveToken(tokenData) {
    try {
      // Ensure the directory exists
      if (!fs.existsSync(CONFIG_DIR)) {
        fs.mkdirSync(CONFIG_DIR, { recursive: true });
      }
      fs.writeFileSync(CONFIG_FILE, JSON.stringify(tokenData, null, 2));
      return true;
    } catch (err) {
      console.error("Error saving token:", err.message);
      return false;
    }
  }

  // -----------------------------
  // Main OAuth Flow
  // -----------------------------

  /**
   * Starts a local server to receive the OAuth callback,
   * then opens the browser to the authorization endpoint.
   */
  async function startOAuthFlow() {
    const CALLBACK_PORT = await findAvailablePort();
    const redirectUri = `http://${CALLBACK_HOST}:${CALLBACK_PORT}${CALLBACK_PATH}`;

    return new Promise((resolve, reject) => {
      const app = express();
      const state = uuidv4();
      // Set up manual code input handler
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
      });

      // Listen for manual code input
      rl.on("line", async (code) => {
        if (!code.trim()) return;

        try {
          const verify = await axios.post(`${SERVER_URL}/api/sso/verify-code`, {
            code: code.trim(),
            client_id: CLIENT_ID,
            client_secret: CLIENT_SECRET,
          });

          if (verify.data.token) {
            const { token, userId, email } = verify.data;
            console.log("\n✅ Authentication successful!");
            console.log(`👤 Logged in as: ${email}`);

            const tokenData = {
              token,
              userId,
              email,
              timestamp: new Date().toISOString(),
            };

            saveToken(tokenData);
            rl.close();
            server.close();
            resolve(tokenData);
          }
        } catch (err) {
          if (
            err.response &&
            err.response.status === 401 &&
            err.response.data &&
            err.response.data.error === "Invalid or expired code"
          ) {
            console.log("\nInvalid code.");
          } else {
            console.log("\nError:", err.message || err);
          }
        }
      });

      app.get(CALLBACK_PATH, async (req, res) => {
        /** Quick helpers */
        const sendError = (msg) => {
          res.send(
            renderHtml({
              title: "OAuth Error",
              heading: "Authentication Error",
              bodyHtml: `<p class="err">${msg}</p>`,
            }),
          );
          server.close();
          rl.close();
          reject(new Error(msg));
        };

        /* Error from provider? */
        if (req.query.error) return sendError(req.query.error);

        /* CSRF check */
        if (req.query.state !== state)
          return sendError("Invalid state parameter");

        /* token from provider */
        const token = req.query.token;
        if (!token) return sendError("No token provided by server");

        /* Verify token */
        try {
          const verify = await axios.post(
            `${SERVER_URL}/api/sso/verify-token`,
            {
              token,
              client_id: CLIENT_ID,
              client_secret: CLIENT_SECRET,
            },
          );

          const { userId, email, authenticated } = verify.data;
          if (!authenticated) {
            return sendError("Token verification failed. Unexpected response.");
          }

          /** success HTML */
          res.send(
            renderHtml({
              title: "OAuth Success",
              heading: "✅ You are authenticated!",
              bodyHtml: `
            <p class="ok">Welcome, <strong>${email}</strong></p>
            <p>User ID: <code>${userId}</code></p>
            <p>Token (first 30 chars):<br>
               <code>${token.slice(0, 30)}…</code></p>
            <p>You can close this tab now.</p>`,
            }),
          );

          const tokenData = {
            token,
            userId,
            email,
            timestamp: new Date().toISOString(),
          };
          saveToken(tokenData);
          rl.close();
          server.close();
          resolve(tokenData);
        } catch (err) {
          const errorDetails =
            err.response?.data?.error ||
            err.message ||
            err.toString() ||
            "Unknown error";
          sendError(`Token verification failed: ${errorDetails}`);
        }
      });

      /** Launch callback server then browser */
      const server = app.listen(CALLBACK_PORT, CALLBACK_HOST, () => {
        // URL for automatic browser flow (no device code needed)
        const autoAuthUrl = new URL(`${SERVER_URL}/api/sso/authorize`);
        autoAuthUrl.searchParams.append("client_id", CLIENT_ID);
        autoAuthUrl.searchParams.append("redirect_uri", redirectUri);
        autoAuthUrl.searchParams.append("state", state);

        // URL for manual flow (shows device code)
        const manualAuthUrl = new URL(`${SERVER_URL}/api/sso/authorize`);
        manualAuthUrl.searchParams.append("client_id", CLIENT_ID);
        manualAuthUrl.searchParams.append("redirect_uri", redirectUri);
        manualAuthUrl.searchParams.append("state", state);
        manualAuthUrl.searchParams.append("device_flow", "true");

        console.log("\n" + "━".repeat(60));
        console.log("🔐 Authentication Required");
        console.log("━".repeat(60));
        console.log("\n📋 Opening browser for authentication...");
        console.log(
          "\n💡 If authentication completes automatically, you're all set!",
        );
        console.log("   Otherwise, visit this URL manually:");
        console.log(`\n   ${manualAuthUrl.toString()}\n`);
        console.log("⌨️  After manual authentication, you'll receive a code");
        console.log("📝 Paste the code below and press Enter:\n");

        // Try to open browser with automatic flow URL
        setTimeout(() => {
          open(autoAuthUrl.toString()).catch(() => {});
        }, 100);
      });

      server.on("error", (err) => reject(err));
    });
  }

  /**
   * Verify if a token is still valid
   */
  async function verifyToken(tokenData) {
    try {
      const verify = await axios.post(`${SERVER_URL}/api/sso/verify-token`, {
        token: tokenData.token,
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
      });
      return verify.data;
    } catch (err) {
      return null;
    }
  }

  /**
   * Clear the token file
   */
  function clearToken() {
    try {
      if (fs.existsSync(CONFIG_FILE)) {
        fs.unlinkSync(CONFIG_FILE);
      }
    } catch (err) {
      console.error("Error clearing token:", err.message);
    }
  }

  // -----------------------------
  // Entry
  // -----------------------------

  (async () => {
    const cached = readToken();
    if (cached) {
      // Verify the token before using it
      const verifyResult = await verifyToken(cached);
      if (verifyResult) {
        // Token is valid, use it
        onAuthenticated(cached);
      } else {
        // Token is invalid, clear it and start OAuth flow
        console.log("⚠️  Cached token is invalid, re-authenticating...");
        clearToken();
        try {
          const tokenData = await startOAuthFlow();
          onAuthenticated(tokenData);
        } catch (err) {
          console.error("OAuth flow failed:", err.message);
          process.exit(1);
        }
      }
    } else {
      try {
        const tokenData = await startOAuthFlow();
        onAuthenticated(tokenData);
      } catch (err) {
        console.error("OAuth flow failed:", err.message);
        process.exit(1);
      }
    }
  })();

  /**
   * Fetch announcements for the authenticated user
   */
  async function fetchAnnouncements(token) {
    try {
      const response = await axios.get(`${SERVER_URL}/api/announcements`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Accept-Language": "en", // Default to English for CLI
        },
      });
      return response.data.announcements || [];
    } catch (err) {
      // If announcements fail to load, just continue silently
      return [];
    }
  }

  /**
   * Display announcements in CLI format and wait for user input
   */
  async function displayAnnouncements(announcements) {
    if (!announcements || announcements.length === 0) {
      return; // No announcements, skip
    }

    console.log("\n" + "═".repeat(70));
    console.log("📢 ANNOUNCEMENTS");
    console.log("═".repeat(70));

    announcements.forEach((announcement, index) => {
      const typeIcon =
        {
          info: "ℹ️",
          warning: "⚠️",
          error: "❌",
          success: "✅",
        }[announcement.type] || "ℹ️";

      console.log(`\n${typeIcon} ${announcement.title}`);
      console.log("─".repeat(50));

      // Strip HTML tags for CLI display
      const plainDescription = announcement.description
        .replace(/<[^>]*>/g, "") // Remove HTML tags
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&amp;/g, "&");

      console.log(plainDescription);

      if (index < announcements.length - 1) {
        console.log(); // Add spacing between announcements
      }
    });

    console.log("\n" + "═".repeat(70));

    return new Promise((resolve) => {
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
      });

      rl.question("📝 Press Enter to continue...\n\n", () => {
        rl.close();
        resolve();
      });
    });
  }

  /** After successful auth */
  async function onAuthenticated({ token, userId, email }) {
    // Check for announcements
    const announcements = await fetchAnnouncements(token);

    // Display announcements if any exist
    if (announcements.length > 0) {
      await displayAnnouncements(announcements);
    }

    // Set up environment variables for cli.js
    const env = {
      ...process.env, // Preserve existing environment variables
      AUTH_TOKEN: token,
      USER_ID: userId,
      USER_EMAIL: email,
      ANTHROPIC_BASE_URL: SERVER_URL + "/claudecode",
    };

    // Run cli.js with the authentication info as environment variables
    const cliPath = path.resolve(__dirname, "cli.js"); // Adjust path as needed
    const cliProcess = spawn("node", [cliPath, ...userArgs], {
      env: env,
      stdio: "inherit", // This will pipe the output to the current process
    });

    cliProcess.on("close", (code) => {
      process.exit(code);
    });

    cliProcess.on("error", (error) => {
      throw error;
    });
  }
})();
