#!/usr/bin/env node

// src/index.tsx
import { render } from "ink";
import { Command } from "commander";

// src/components/App.tsx
import { useState as useState6, useCallback } from "react";
import { Box as Box7, Text as Text7 } from "ink";

// src/components/DiscoveryScreen.tsx
import { useState, useEffect } from "react";
import { Box as Box2, Text as Text2 } from "ink";

// src/lib/discovery.ts
import fetch from "node-fetch";

// src/lib/dns.ts
import { promisify } from "node:util";
import { lookup } from "node:dns";
var dnsLookup = promisify(lookup);
async function resolveDomain(domain, maxRetries = 3) {
  let lastError = null;
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e3);
      const result = await Promise.race([
        dnsLookup(domain),
        new Promise((_, reject) => {
          const timer = setTimeout(() => reject(new Error("DNS timeout")), 3e3);
          controller.signal.addEventListener("abort", () => {
            clearTimeout(timer);
            reject(new Error("DNS aborted"));
          });
        })
      ]);
      clearTimeout(timeoutId);
      return true;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      const errorMessage = lastError.message.toLowerCase();
      const isNXDomain = errorMessage.includes("notfound") || errorMessage.includes("enotfound");
      if (isNXDomain) {
        return false;
      }
      if (attempt < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt - 1) * 100));
      }
    }
  }
  return false;
}

// src/lib/discovery.ts
var RelayDiscovery = class {
  discoveredHosts = /* @__PURE__ */ new Set();
  scanQueue = /* @__PURE__ */ new Set();
  relayServers = /* @__PURE__ */ new Map();
  concurrency;
  timeout;
  onProgress;
  stopped = false;
  activePromises = /* @__PURE__ */ new Set();
  constructor(concurrency = 10, timeout = 3e3, onProgress) {
    this.concurrency = concurrency;
    this.timeout = timeout;
    this.onProgress = onProgress;
  }
  async startDiscovery(baseDomain) {
    this.scanQueue.add(baseDomain);
    for (let i = 1; i <= 99; i++) {
      const host = `relay${i.toString().padStart(2, "0")}.${baseDomain}`;
      this.scanQueue.add(host);
    }
    await this.processDiscoveryQueue();
    return Array.from(this.relayServers.values());
  }
  stop() {
    this.stopped = true;
    this.scanQueue.clear();
    this.activePromises.clear();
  }
  async processDiscoveryQueue() {
    while (!this.stopped) {
      if (this.scanQueue.size === 0 && this.activePromises.size === 0) {
        break;
      }
      if (this.scanQueue.size > 0) {
        const currentBatch = Array.from(this.scanQueue).slice(0, this.concurrency);
        currentBatch.forEach((host) => this.scanQueue.delete(host));
        const promises = currentBatch.map((host) => {
          const promise = this.processHost(host);
          this.activePromises.add(promise);
          return promise.finally(() => {
            this.activePromises.delete(promise);
          });
        });
        await Promise.all(promises);
      }
      if (this.scanQueue.size === 0 && this.activePromises.size > 0) {
        await Promise.all(Array.from(this.activePromises));
      }
      this.updateProgress();
    }
  }
  async processHost(host) {
    if (this.discoveredHosts.has(host) || this.stopped) {
      return;
    }
    this.discoveredHosts.add(host);
    try {
      const isIpAddress = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d+)?$/.test(host);
      if (!isIpAddress) {
        const dnsResolved = await resolveDomain(host);
        if (!dnsResolved) {
          this.updateProgress();
          return;
        }
      }
      const relayServer = {
        host,
        info: null,
        metrics: null,
        status: "testing",
        retryCount: 0
      };
      this.relayServers.set(host, relayServer);
      this.updateProgress();
      await this.fetchRelayInfoWithRetry(relayServer);
    } catch (error) {
      this.updateProgress();
    }
  }
  async fetchRelayInfoWithRetry(relayServer) {
    const maxRetries = 3;
    let lastError = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      relayServer.retryCount = attempt - 1;
      try {
        const result = await this.fetchRelayInfo(relayServer.host);
        relayServer.info = result.info;
        relayServer.insecure = result.insecure;
        relayServer.status = "online";
        if (result.info.peers && Array.isArray(result.info.peers) && result.info.peers.length > 0) {
          result.info.peers.forEach((peer) => {
            try {
              const normalizedPeer = this.normalizePeerHost(peer);
              if (normalizedPeer && !this.discoveredHosts.has(normalizedPeer) && !this.scanQueue.has(normalizedPeer)) {
                this.scanQueue.add(normalizedPeer);
              }
            } catch (peerError) {
              console.error(`Error processing peer ${peer}:`, peerError);
            }
          });
        }
        this.updateProgress();
        return;
      } catch (fetchError) {
        lastError = fetchError;
      }
      if (attempt < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, 1e3 * attempt));
      }
    }
    relayServer.status = "error";
    relayServer.retryCount = maxRetries;
    if (lastError) {
      console.log(`Relay ${relayServer.host} failed: ${lastError.message}`);
    }
    this.updateProgress();
  }
  normalizePeerHost(peer) {
    if (!peer || peer.trim() === "") {
      return null;
    }
    peer = peer.trim();
    if (/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(peer)) {
      return peer;
    }
    const ipPortMatch = peer.match(/^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d+)$/);
    if (ipPortMatch) {
      return peer;
    }
    return peer;
  }
  buildRelayUrl(host) {
    if (host.includes(":") && /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$/.test(host)) {
      return `https://${host}/relay/info`;
    }
    return `https://${host}/relay/info`;
  }
  async fetchRelayInfo(host) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3e3);
      const response = await fetch(this.buildRelayUrl(host), {
        signal: controller.signal,
        headers: {
          "User-Agent": "relay-selector/1.0.0"
        }
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status} ${response.statusText}`);
      }
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        throw new Error(`Invalid JSON response: ${jsonError instanceof Error ? jsonError.message : String(jsonError)}`);
      }
      if (!data || typeof data !== "object") {
        throw new Error(`Invalid response structure: expected object, got ${typeof data}`);
      }
      return {
        info: {
          host,
          description: data.description || "Unknown Relay",
          attributes: data.attributes || {},
          peers: Array.isArray(data.peers) ? data.peers : []
        },
        insecure: false
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isTLSError = errorMessage.includes("certificate") || errorMessage.includes("TLS") || errorMessage.includes("SSL") || errorMessage.includes("self signed") || errorMessage.includes("CERT_") || errorMessage.includes("unable to verify");
      if (!isTLSError) {
        throw error;
      }
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3e3);
        const https = await import("node:https");
        const agent = new https.Agent({
          rejectUnauthorized: false
        });
        const response = await fetch(this.buildRelayUrl(host), {
          signal: controller.signal,
          headers: {
            "User-Agent": "relay-selector/1.0.0"
          },
          // @ts-ignore - agent property exists in node-fetch
          agent
        });
        clearTimeout(timeoutId);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status} ${response.statusText} (insecure)`);
        }
        let data;
        try {
          data = await response.json();
        } catch (jsonError) {
          throw new Error(`Invalid JSON response (insecure): ${jsonError instanceof Error ? jsonError.message : String(jsonError)}`);
        }
        if (!data || typeof data !== "object") {
          throw new Error(`Invalid response structure (insecure): expected object, got ${typeof data}`);
        }
        return {
          info: {
            host,
            description: data.description || "Unknown Relay",
            attributes: data.attributes || {},
            peers: Array.isArray(data.peers) ? data.peers : []
          },
          insecure: true
        };
      } catch (insecureError) {
        throw insecureError;
      }
    }
  }
  updateProgress() {
    if (!this.onProgress) return;
    const servers = Array.from(this.relayServers.values());
    const total = this.discoveredHosts.size + this.scanQueue.size;
    const completed = this.discoveredHosts.size;
    const currentHost = this.scanQueue.size > 0 ? Array.from(this.scanQueue)[0] : "";
    let phase = "complete";
    if (this.scanQueue.size > 0 || this.activePromises.size > 0) {
      const hasOnlineServers = servers.some((s) => s.status === "online");
      phase = hasOnlineServers ? "info" : "dns";
    }
    const progress = {
      total,
      completed,
      current: currentHost,
      phase
    };
    this.onProgress(progress, servers);
  }
  // Get current state for external access
  getState() {
    const servers = Array.from(this.relayServers.values());
    const total = this.discoveredHosts.size + this.scanQueue.size;
    const completed = this.discoveredHosts.size;
    const currentHost = this.scanQueue.size > 0 ? Array.from(this.scanQueue)[0] : "";
    let phase = "complete";
    if (this.scanQueue.size > 0 || this.activePromises.size > 0) {
      const hasOnlineServers = servers.some((s) => s.status === "online");
      phase = hasOnlineServers ? "info" : "dns";
    }
    return {
      progress: {
        total,
        completed,
        current: currentHost,
        phase
      },
      servers
    };
  }
};

// src/components/ProgressBar.tsx
import { Box, Text } from "ink";
import { jsx, jsxs } from "react/jsx-runtime";
var ProgressBar = ({
  current,
  total,
  width = 40,
  color = "green"
}) => {
  const percentage = total > 0 ? Math.round(current / total * 100) : 0;
  const filled = Math.round(current / total * width);
  const empty = width - filled;
  const filledBar = "\u2588".repeat(Math.max(0, filled));
  const emptyBar = "\u2591".repeat(Math.max(0, empty));
  return /* @__PURE__ */ jsxs(Box, { children: [
    /* @__PURE__ */ jsx(Text, { color, children: filledBar }),
    /* @__PURE__ */ jsx(Text, { color: "gray", children: emptyBar }),
    /* @__PURE__ */ jsx(Text, { children: " " }),
    /* @__PURE__ */ jsxs(Text, { color, children: [
      current,
      "/",
      total
    ] }),
    /* @__PURE__ */ jsxs(Text, { color: "gray", children: [
      " (",
      percentage,
      "%)"
    ] })
  ] });
};

// src/components/DiscoveryScreen.tsx
import { jsx as jsx2, jsxs as jsxs2 } from "react/jsx-runtime";
var DiscoveryScreen = ({
  baseDomain,
  onComplete,
  onStart
}) => {
  const [progress, setProgress] = useState({
    total: 0,
    completed: 0,
    current: "",
    phase: "dns"
  });
  const [servers, setServers] = useState([]);
  const [discovery] = useState(() => new RelayDiscovery(
    15,
    // concurrency
    3e3,
    // timeout
    (newProgress, newServers) => {
      setProgress(newProgress);
      setServers(newServers);
    }
  ));
  useEffect(() => {
    let isMounted = true;
    const runDiscovery = async () => {
      try {
        const startTime = /* @__PURE__ */ new Date();
        onStart == null ? void 0 : onStart(startTime);
        const finalServers = await discovery.startDiscovery(baseDomain);
        if (isMounted) {
          onComplete(finalServers);
        }
      } catch (error) {
        console.error("Discovery failed:", error);
      }
    };
    runDiscovery();
    return () => {
      isMounted = false;
      discovery.stop();
    };
  }, [baseDomain, discovery]);
  const getPhaseDescription = (phase) => {
    switch (phase) {
      case "dns":
        return "Resolving DNS...";
      case "info":
        return "Fetching relay info...";
      default:
        return "Scanning...";
    }
  };
  const getPhaseColor = (phase) => {
    switch (phase) {
      case "dns":
        return "blue";
      case "info":
        return "yellow";
      default:
        return "white";
    }
  };
  const onlineServers = servers.filter((s) => s.status === "online");
  const offlineCount = servers.filter((s) => s.status === "offline").length;
  const errorCount = servers.filter((s) => s.status === "error").length;
  return /* @__PURE__ */ jsxs2(Box2, { flexDirection: "column", padding: 1, children: [
    /* @__PURE__ */ jsx2(Box2, { marginBottom: 1, children: /* @__PURE__ */ jsx2(Text2, { bold: true, color: "cyan", children: "\u{1F50D} Relay Network Discovery" }) }),
    /* @__PURE__ */ jsxs2(Box2, { marginBottom: 1, children: [
      /* @__PURE__ */ jsx2(Text2, { children: "Domain: " }),
      /* @__PURE__ */ jsx2(Text2, { color: "green", children: baseDomain }),
      /* @__PURE__ */ jsx2(Text2, { children: " | Scanning: canonical + relay01-relay99" })
    ] }),
    /* @__PURE__ */ jsx2(Box2, { marginBottom: 1, children: /* @__PURE__ */ jsx2(
      ProgressBar,
      {
        current: progress.completed,
        total: progress.total,
        color: getPhaseColor(progress.phase)
      }
    ) }),
    /* @__PURE__ */ jsxs2(Box2, { marginBottom: 1, children: [
      /* @__PURE__ */ jsx2(Text2, { color: getPhaseColor(progress.phase), children: getPhaseDescription(progress.phase) }),
      progress.current && /* @__PURE__ */ jsxs2(Text2, { color: "gray", children: [
        " (",
        progress.current,
        ")"
      ] })
    ] }),
    /* @__PURE__ */ jsxs2(Box2, { marginBottom: 1, children: [
      /* @__PURE__ */ jsx2(Text2, { children: "\u{1F4CA} Status: " }),
      /* @__PURE__ */ jsxs2(Text2, { color: "green", children: [
        onlineServers.length,
        " online"
      ] }),
      /* @__PURE__ */ jsx2(Text2, { children: " | " }),
      /* @__PURE__ */ jsxs2(Text2, { color: "red", children: [
        offlineCount,
        " offline"
      ] }),
      /* @__PURE__ */ jsx2(Text2, { children: " | " }),
      /* @__PURE__ */ jsxs2(Text2, { color: "yellow", children: [
        errorCount,
        " errors"
      ] }),
      /* @__PURE__ */ jsx2(Text2, { children: " | " }),
      /* @__PURE__ */ jsxs2(Text2, { color: "gray", children: [
        servers.filter((s) => s.status === "testing").length,
        " testing"
      ] })
    ] })
  ] });
};

// src/components/TestingScreen.tsx
import { useState as useState2, useEffect as useEffect2 } from "react";
import { Box as Box3, Text as Text3 } from "ink";

// src/lib/performance.ts
import fetch2 from "node-fetch";
import { randomBytes } from "crypto";
import { Socket } from "net";
var PerformanceTester = class {
  timeout;
  testEndpoint;
  bandwidthTestData;
  constructor(timeout = 3e3, testEndpoint = "/relay/info") {
    this.timeout = timeout;
    this.testEndpoint = testEndpoint;
    this.bandwidthTestData = randomBytes(500 * 1024).toString("base64");
  }
  buildTestUrl(host) {
    if (host.includes(":") && /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$/.test(host)) {
      return `https://${host}${this.testEndpoint}`;
    }
    return `https://${host}${this.testEndpoint}`;
  }
  async tcpConnectTime(host) {
    return new Promise((resolve) => {
      let hostname;
      let port;
      try {
        if (host.includes(":")) {
          const parts = host.split(":");
          hostname = parts[0];
          port = parseInt(parts[1], 10);
          if (isNaN(port) || port < 1 || port > 65535) {
            resolve({ latency: 3e3, available: false });
            return;
          }
        } else {
          hostname = host;
          port = 443;
        }
        const startTime = Date.now();
        const socket = new Socket();
        socket.setTimeout(3e3);
        socket.on("connect", () => {
          const latency = Date.now() - startTime;
          socket.destroy();
          resolve({ latency, available: true });
        });
        socket.on("timeout", () => {
          socket.destroy();
          resolve({ latency: 3e3, available: false });
        });
        socket.on("error", (error) => {
          socket.destroy();
          const latency = Date.now() - startTime;
          console.error(`TCP connect error for ${host}: ${error.message}`);
          resolve({ latency, available: false });
        });
        socket.connect(port, hostname);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`TCP setup error for ${host}: ${errorMessage}`);
        resolve({ latency: 3e3, available: false });
      }
    });
  }
  async testRelay(server) {
    const tcpResult = await this.tcpConnectTime(server.host);
    return {
      host: server.host,
      latency: tcpResult.latency,
      available: tcpResult.available,
      responseTime: tcpResult.latency,
      errorCount: tcpResult.available ? 0 : 1,
      lastChecked: /* @__PURE__ */ new Date()
    };
  }
  async testMultipleRelays(servers, concurrency = 10, onProgress) {
    const results = [];
    let completed = 0;
    for (const server of servers) {
      if (onProgress) {
        onProgress(completed, servers.length, server.host, "ping");
      }
      const metrics = await this.testRelay(server);
      if (metrics.available) {
        if (onProgress) {
          onProgress(completed, servers.length, server.host, "bandwidth");
        }
        const bandwidthResult = await this.testBandwidth(server);
        metrics.bandwidthTest = bandwidthResult;
      }
      results.push(metrics);
      completed++;
      if (onProgress) {
        onProgress(completed, servers.length, server.host, "ping");
      }
    }
    return results;
  }
  rankRelays(servers) {
    const serversWithMetrics = servers.filter((s) => s.metrics && s.status === "online");
    serversWithMetrics.sort((a, b) => {
      const scoreA = this.calculateScore(a.metrics);
      const scoreB = this.calculateScore(b.metrics);
      return scoreA - scoreB;
    });
    serversWithMetrics.forEach((server, index) => {
      server.rank = index + 1;
    });
    const unrankedServers = servers.filter((s) => !s.metrics || s.status !== "online");
    return [...serversWithMetrics, ...unrankedServers];
  }
  async testBandwidth(server) {
    const bandwidthTimeout = 5e3;
    let wasAbortedByTimeout = false;
    const startTime = Date.now();
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        wasAbortedByTimeout = true;
        controller.abort();
      }, bandwidthTimeout);
      const fetchOptions = {
        signal: controller.signal,
        method: "POST",
        headers: {
          "Content-Type": "text/plain",
          "User-Agent": "relay-selector/1.0.0"
        },
        body: this.bandwidthTestData
      };
      if (server.insecure) {
        const https = await import("node:https");
        fetchOptions.agent = new https.Agent({
          rejectUnauthorized: false
        });
      }
      const response = await fetch2(this.buildBandwidthTestUrl(server.host), fetchOptions);
      const uploadTime = Date.now() - startTime;
      const downloadStartTime = Date.now();
      const responseData = await response.text();
      const downloadTime = Date.now() - downloadStartTime;
      clearTimeout(timeoutId);
      const totalTime = Date.now() - startTime;
      const success = response.ok && responseData.length > 4e5;
      return {
        uploadTime,
        downloadTime,
        totalTime,
        success,
        aborted: false
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (!server.insecure && !wasAbortedByTimeout) {
        const isTLSError = errorMessage.includes("certificate") || errorMessage.includes("TLS") || errorMessage.includes("SSL") || errorMessage.includes("self signed") || errorMessage.includes("CERT_") || errorMessage.includes("unable to verify");
        if (isTLSError) {
          try {
            let retryWasAbortedByTimeout = false;
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
              retryWasAbortedByTimeout = true;
              controller.abort();
            }, bandwidthTimeout);
            const https = await import("node:https");
            const agent = new https.Agent({
              rejectUnauthorized: false
            });
            const startTime2 = Date.now();
            const response = await fetch2(this.buildBandwidthTestUrl(server.host), {
              signal: controller.signal,
              method: "POST",
              headers: {
                "Content-Type": "text/plain",
                "User-Agent": "relay-selector/1.0.0"
              },
              body: this.bandwidthTestData,
              // @ts-ignore
              agent
            });
            const uploadTime = Date.now() - startTime2;
            const downloadStartTime = Date.now();
            const responseData = await response.text();
            const downloadTime = Date.now() - downloadStartTime;
            clearTimeout(timeoutId);
            const totalTime = Date.now() - startTime2;
            const success = response.ok && responseData.length > 4e5;
            return {
              uploadTime,
              downloadTime,
              totalTime,
              success,
              aborted: false
            };
          } catch (retryError) {
            return {
              uploadTime: 0,
              downloadTime: 0,
              totalTime: wasAbortedByTimeout ? 5e3 : Date.now() - startTime,
              success: false,
              aborted: wasAbortedByTimeout
            };
          }
        }
      }
      return {
        uploadTime: 0,
        downloadTime: 0,
        totalTime: wasAbortedByTimeout ? 5e3 : Date.now() - startTime,
        success: false,
        aborted: wasAbortedByTimeout
      };
    }
  }
  buildBandwidthTestUrl(host) {
    if (host.includes(":") && /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+$/.test(host)) {
      return `https://${host}/api/bandwidth-test`;
    }
    return `https://${host}/api/bandwidth-test`;
  }
  calculateScore(metrics) {
    let score = metrics.latency;
    if (metrics.errorCount > 0) {
      score += 1e4;
    }
    if (!metrics.available) {
      score += 2e4;
    }
    if (metrics.bandwidthTest) {
      if (!metrics.bandwidthTest.success) {
        score += 5e3;
      } else {
        score += metrics.bandwidthTest.totalTime / 10;
      }
    }
    return score;
  }
  getPerformanceGrade(metrics) {
    if (!metrics.available) {
      return { grade: "F", color: "red" };
    }
    if (metrics.latency < 100) {
      return { grade: "A+", color: "green" };
    } else if (metrics.latency < 200) {
      return { grade: "A", color: "green" };
    } else if (metrics.latency < 500) {
      return { grade: "B", color: "yellow" };
    } else if (metrics.latency < 1e3) {
      return { grade: "C", color: "yellow" };
    } else if (metrics.latency < 2e3) {
      return { grade: "D", color: "red" };
    } else {
      return { grade: "F", color: "red" };
    }
  }
};

// src/components/TestingScreen.tsx
import { jsx as jsx3, jsxs as jsxs3 } from "react/jsx-runtime";
var TestingScreen = ({
  servers,
  onComplete
}) => {
  const [testProgress, setTestProgress] = useState2({ current: 0, total: 1, currentHost: "", phase: "ping" });
  const [isComplete, setIsComplete] = useState2(false);
  useEffect2(() => {
    let isMounted = true;
    const testAndRankServers = async () => {
      try {
        const performanceTester = new PerformanceTester();
        const onlineServers = servers.filter((s) => s.status === "online");
        if (onlineServers.length === 0) {
          if (isMounted) {
            onComplete(servers);
          }
          return;
        }
        setTestProgress({ current: 0, total: Math.max(1, onlineServers.length), currentHost: "", phase: "ping" });
        const metrics = await performanceTester.testMultipleRelays(
          onlineServers,
          1,
          // Process sequentially for smooth progress
          (tested, total, current, phase = "ping") => {
            if (isMounted) {
              setTestProgress({
                current: tested,
                total,
                currentHost: current,
                phase
              });
            }
          }
        );
        const serversWithMetrics = servers.map((server) => {
          const metric = metrics.find((m) => m.host === server.host);
          if (metric) {
            return { ...server, metrics: metric };
          }
          return server;
        });
        const rankedServers = performanceTester.rankRelays(serversWithMetrics);
        if (isMounted) {
          setIsComplete(true);
          onComplete(rankedServers);
        }
      } catch (error) {
        console.error("Testing failed:", error);
        if (isMounted) {
          onComplete(servers);
        }
      }
    };
    testAndRankServers();
    return () => {
      isMounted = false;
    };
  }, [servers]);
  return /* @__PURE__ */ jsxs3(Box3, { flexDirection: "column", padding: 1, children: [
    /* @__PURE__ */ jsx3(Box3, { marginBottom: 1, children: /* @__PURE__ */ jsx3(Text3, { bold: true, color: "magenta", children: "\u26A1 Testing Relay Performance" }) }),
    /* @__PURE__ */ jsx3(Box3, { marginBottom: 1, children: /* @__PURE__ */ jsx3(
      ProgressBar,
      {
        current: testProgress.current,
        total: testProgress.total,
        color: "magenta"
      }
    ) }),
    /* @__PURE__ */ jsx3(Box3, { children: /* @__PURE__ */ jsx3(Text3, { color: "gray", children: isComplete ? "Ranking complete!" : `${testProgress.phase === "ping" ? "Ping" : "Bandwidth"} test: ${testProgress.currentHost}` }) }),
    testProgress.phase === "bandwidth" && /* @__PURE__ */ jsx3(Box3, { marginTop: 1, children: /* @__PURE__ */ jsx3(Text3, { color: "yellow", children: "\u{1F4CA} Running bandwidth tests sequentially (500KB upload + download)" }) })
  ] });
};

// src/components/SelectionScreen.tsx
import { useState as useState3, useMemo } from "react";
import { Box as Box4, Text as Text4, useInput } from "ink";
import { Fragment, jsx as jsx4, jsxs as jsxs4 } from "react/jsx-runtime";
var SelectionScreen = ({
  servers,
  onSelect,
  onBack,
  baseDomain
}) => {
  const [selectedIndex, setSelectedIndex] = useState3(0);
  const isCanonical = (server) => {
    return server.host === baseDomain;
  };
  const performanceTester = new PerformanceTester();
  const displayServers = useMemo(
    () => servers.filter((s) => s.status === "online" && s.metrics),
    [servers]
  );
  useInput((input, key) => {
    if (key.upArrow) {
      setSelectedIndex((prev) => Math.max(0, prev - 1));
    } else if (key.downArrow) {
      setSelectedIndex((prev) => Math.min(displayServers.length - 1, prev + 1));
    } else if (key.return) {
      if (displayServers[selectedIndex]) {
        onSelect(displayServers[selectedIndex]);
      }
    } else if (key.escape || input === "r") {
      onBack();
    }
  });
  return /* @__PURE__ */ jsxs4(Box4, { flexDirection: "column", padding: 1, children: [
    /* @__PURE__ */ jsx4(Box4, { marginBottom: 1, children: /* @__PURE__ */ jsx4(Text4, { bold: true, color: "cyan", children: "\u{1F680} Select Best Relay Server" }) }),
    /* @__PURE__ */ jsxs4(Box4, { marginBottom: 1, flexDirection: "column", children: [
      /* @__PURE__ */ jsx4(Text4, { color: "gray", children: "Use \u2191\u2193 to navigate, Enter to select, ESC/r to rerun" }),
      /* @__PURE__ */ jsx4(Text4, { color: "gray", children: "\u26A1 Ping Grade: Response latency (A+ best, F worst)" }),
      /* @__PURE__ */ jsx4(Text4, { color: "gray", children: "\u{1F4CA} Bandwidth: 500KB upload + download testing data transfer latency for LLM conversations" }),
      /* @__PURE__ */ jsx4(Text4, { color: "gray", children: "\u{1F4CD} Location: Geographic location of relay server" }),
      /* @__PURE__ */ jsx4(Text4, { color: "gray", children: "\u2601\uFE0F Network Route: Type of international route used for optimized connectivity" }),
      /* @__PURE__ */ jsx4(Text4, { color: "gray", children: "\u{1F310} Host: Server hostname or IP address" })
    ] }),
    /* @__PURE__ */ jsx4(Box4, { flexDirection: "column", children: displayServers.map((server, index) => {
      var _a, _b, _c, _d, _e;
      const isSelected = index === selectedIndex;
      const grade = server.metrics ? performanceTester.getPerformanceGrade(server.metrics) : { grade: "F", color: "red" };
      const geoLocation = ((_a = server.info) == null ? void 0 : _a.attributes.location) || "Unknown";
      return /* @__PURE__ */ jsxs4(Box4, { marginBottom: 2, flexDirection: "column", children: [
        /* @__PURE__ */ jsxs4(Box4, { children: [
          /* @__PURE__ */ jsx4(Text4, { color: "white", children: isSelected ? "\u2192" : " " }),
          /* @__PURE__ */ jsxs4(Text4, { color: isSelected ? "green" : "gray", bold: true, children: [
            "#",
            server.rank
          ] }),
          /* @__PURE__ */ jsx4(Text4, { children: " " }),
          isCanonical(server) && /* @__PURE__ */ jsx4(Text4, { color: "yellow", bold: true, children: "[CANONICAL] " }),
          /* @__PURE__ */ jsx4(Text4, { color: isSelected ? "white" : void 0, children: ((_b = server.info) == null ? void 0 : _b.description) || server.host })
        ] }),
        isCanonical(server) && /* @__PURE__ */ jsx4(Box4, { marginLeft: 3, paddingX: 1, borderStyle: "single", borderColor: "green", children: /* @__PURE__ */ jsxs4(Text4, { color: "green", children: [
          "\u{1F4A1} ",
          /* @__PURE__ */ jsx4(Text4, { bold: true, children: "Recommended" }),
          ": This is the official auto-managed relay with best-effort uptime and reliability. ",
          /* @__PURE__ */ jsx4(Text4, { color: "yellow", children: "If you choose to use other relays and they go down, you will need to rerun this tool to pick another relay." })
        ] }) }),
        /* @__PURE__ */ jsxs4(Box4, { marginLeft: 3, children: [
          /* @__PURE__ */ jsx4(Text4, { color: "gray", children: "\u26A1 " }),
          /* @__PURE__ */ jsx4(Text4, { color: grade.color, bold: true, children: grade.grade }),
          /* @__PURE__ */ jsxs4(Text4, { color: "gray", children: [
            " (",
            ((_c = server.metrics) == null ? void 0 : _c.latency) || 0,
            "ms)"
          ] }),
          ((_d = server.metrics) == null ? void 0 : _d.bandwidthTest) && /* @__PURE__ */ jsxs4(Fragment, { children: [
            /* @__PURE__ */ jsx4(Text4, { color: "gray", children: " \u2022 \u{1F4CA} " }),
            server.metrics.bandwidthTest.success ? /* @__PURE__ */ jsxs4(Text4, { color: "green", children: [
              server.metrics.bandwidthTest.totalTime,
              "ms"
            ] }) : server.metrics.bandwidthTest.aborted ? /* @__PURE__ */ jsx4(Text4, { color: "red", children: "5000ms+" }) : /* @__PURE__ */ jsx4(Text4, { color: "red", children: "failed" })
          ] }),
          /* @__PURE__ */ jsx4(Text4, { color: "gray", children: " \u2022 \u{1F4CD} " }),
          /* @__PURE__ */ jsx4(Text4, { color: "cyan", children: geoLocation }),
          ((_e = server.info) == null ? void 0 : _e.attributes.provider) && /* @__PURE__ */ jsxs4(Fragment, { children: [
            /* @__PURE__ */ jsx4(Text4, { color: "gray", children: " \u2022 \u2601\uFE0F " }),
            /* @__PURE__ */ jsx4(Text4, { color: "blue", children: server.info.attributes.provider })
          ] }),
          /* @__PURE__ */ jsx4(Text4, { color: "gray", children: " \u2022 \u{1F310} " }),
          /* @__PURE__ */ jsx4(Text4, { color: "yellow", children: server.host })
        ] })
      ] }, server.host);
    }) }),
    displayServers.length === 0 && /* @__PURE__ */ jsx4(Box4, { marginTop: 2, children: /* @__PURE__ */ jsx4(Text4, { color: "red", children: "\u274C No online relay servers found" }) })
  ] });
};

// src/components/InsecureWarningScreen.tsx
import { useState as useState4 } from "react";
import { Box as Box5, Text as Text5, useInput as useInput2 } from "ink";
import { jsx as jsx5, jsxs as jsxs5 } from "react/jsx-runtime";
var InsecureWarningScreen = ({
  server,
  onConfirm,
  onCancel
}) => {
  const [selectedOption, setSelectedOption] = useState4("confirm");
  useInput2((input, key) => {
    if (key.leftArrow || key.rightArrow) {
      setSelectedOption((prev) => prev === "cancel" ? "confirm" : "cancel");
    } else if (key.return) {
      if (selectedOption === "confirm") {
        const insecureServer = { ...server, insecure: true };
        onConfirm(insecureServer);
      } else {
        onCancel();
      }
    } else if (key.escape || input === "n") {
      onCancel();
    } else if (input === "y") {
      const insecureServer = { ...server, insecure: true };
      onConfirm(insecureServer);
    }
  });
  return /* @__PURE__ */ jsxs5(Box5, { flexDirection: "column", padding: 2, borderStyle: "single", borderColor: "blue", children: [
    /* @__PURE__ */ jsx5(Box5, { marginBottom: 1, children: /* @__PURE__ */ jsx5(Text5, { bold: true, color: "blue", children: "\u2139\uFE0F  Security Notice" }) }),
    /* @__PURE__ */ jsxs5(Box5, { flexDirection: "column", marginBottom: 2, children: [
      /* @__PURE__ */ jsx5(Text5, { children: "The selected relay server uses a self-signed certificate:" }),
      /* @__PURE__ */ jsx5(Text5, { bold: true, color: "cyan", children: server.host })
    ] }),
    /* @__PURE__ */ jsxs5(Box5, { flexDirection: "column", marginBottom: 2, children: [
      /* @__PURE__ */ jsx5(Text5, { color: "gray", children: "\u{1F4A1} This relay is generally safe for normal use. In rare cases, your WiFi" }),
      /* @__PURE__ */ jsx5(Text5, { color: "gray", children: "provider or ISP might potentially view traffic, though such attacks are" }),
      /* @__PURE__ */ jsx5(Text5, { color: "gray", children: "uncommon. For highly sensitive work, consider a relay with valid TLS." })
    ] }),
    /* @__PURE__ */ jsx5(Box5, { marginBottom: 1, children: /* @__PURE__ */ jsx5(Text5, { bold: true, children: "Do you want to proceed with this relay?" }) }),
    /* @__PURE__ */ jsxs5(Box5, { children: [
      /* @__PURE__ */ jsx5(
        Box5,
        {
          paddingX: 1,
          marginRight: 2,
          children: /* @__PURE__ */ jsx5(Text5, { color: selectedOption === "cancel" ? "blue" : "gray", bold: true, children: "Choose another (N)" })
        }
      ),
      /* @__PURE__ */ jsx5(
        Box5,
        {
          paddingX: 1,
          children: /* @__PURE__ */ jsx5(Text5, { color: selectedOption === "confirm" ? "green" : "gray", bold: true, children: "Continue (Y)" })
        }
      )
    ] }),
    /* @__PURE__ */ jsx5(Box5, { marginTop: 1, children: /* @__PURE__ */ jsx5(Text5, { color: "gray", children: "Use \u2190\u2192 arrows to select, Enter to confirm, ESC to cancel" }) })
  ] });
};

// src/components/CountdownRetryScreen.tsx
import { useState as useState5, useEffect as useEffect4 } from "react";
import { Box as Box6, Text as Text6 } from "ink";
import { jsx as jsx6, jsxs as jsxs6 } from "react/jsx-runtime";
var CountdownRetryScreen = ({
  onRetry,
  onCancel,
  discoveredCount,
  countdownSeconds = 6
}) => {
  const [secondsLeft, setSecondsLeft] = useState5(countdownSeconds);
  useEffect4(() => {
    if (secondsLeft <= 0) {
      onRetry();
      return;
    }
    const timer = setTimeout(() => {
      setSecondsLeft((prev) => prev - 1);
    }, 1e3);
    return () => clearTimeout(timer);
  }, [secondsLeft, onRetry]);
  useEffect4(() => {
    const handleSignal = () => {
      onCancel();
    };
    process.on("SIGINT", handleSignal);
    process.on("SIGTERM", handleSignal);
    return () => {
      process.off("SIGINT", handleSignal);
      process.off("SIGTERM", handleSignal);
    };
  }, [onCancel]);
  return /* @__PURE__ */ jsxs6(Box6, { flexDirection: "column", padding: 1, children: [
    /* @__PURE__ */ jsx6(Box6, { marginBottom: 1, children: /* @__PURE__ */ jsx6(Text6, { bold: true, color: "yellow", children: "\u26A0\uFE0F  No Relay Servers Found" }) }),
    /* @__PURE__ */ jsx6(Box6, { marginBottom: 1, children: /* @__PURE__ */ jsxs6(Text6, { children: [
      "Discovered ",
      discoveredCount,
      " servers, none are currently reachable."
    ] }) }),
    /* @__PURE__ */ jsx6(Box6, { marginBottom: 1, children: /* @__PURE__ */ jsx6(Text6, { color: "gray", children: "This might be due to network cold start issues, retrying might help." }) }),
    /* @__PURE__ */ jsx6(Box6, { marginBottom: 2, children: /* @__PURE__ */ jsxs6(Text6, { color: "cyan", children: [
      "\u{1F504} Automatically retrying in ",
      secondsLeft,
      " second",
      secondsLeft !== 1 ? "s" : "",
      "..."
    ] }) }),
    /* @__PURE__ */ jsx6(Box6, { marginBottom: 1, children: /* @__PURE__ */ jsx6(Text6, { color: "gray", children: "Press Ctrl+C to cancel and exit" }) })
  ] });
};

// src/lib/relay-storage.ts
import { writeFileSync, mkdirSync } from "node:fs";
import { join } from "node:path";
import { homedir } from "node:os";
function writeRelayResult(selectedServer) {
  var _a, _b, _c, _d, _e;
  try {
    const homeDir = homedir();
    const claudecodeDir = join(homeDir, ".claudecode");
    const relayFilePath = join(claudecodeDir, "relay");
    try {
      mkdirSync(claudecodeDir, { recursive: true });
    } catch (error) {
    }
    const relayData = {
      host: selectedServer.host,
      description: (_a = selectedServer.info) == null ? void 0 : _a.description,
      location: (_b = selectedServer.info) == null ? void 0 : _b.attributes.location,
      region: (_c = selectedServer.info) == null ? void 0 : _c.attributes.region,
      provider: (_d = selectedServer.info) == null ? void 0 : _d.attributes.provider,
      latency: (_e = selectedServer.metrics) == null ? void 0 : _e.latency,
      rank: selectedServer.rank,
      insecure: selectedServer.insecure || false,
      selectedAt: (/* @__PURE__ */ new Date()).toISOString()
    };
    writeFileSync(relayFilePath, JSON.stringify(relayData, null, 2));
    console.log(`Relay selection saved to ${relayFilePath}`);
  } catch (error) {
    console.error("Failed to save relay selection:", error);
  }
}

// src/lib/report-generator.ts
import { writeFileSync as writeFileSync2 } from "fs";
import { join as join2 } from "path";
import { homedir as homedir2 } from "os";
function generateReport(options2) {
  var _a, _b;
  const { baseDomain, selectedServer, discoveredServers, scanStartTime, scanEndTime } = options2;
  const now = /* @__PURE__ */ new Date();
  const onlineServers = discoveredServers.filter((s) => s.status === "online");
  const offlineServers = discoveredServers.filter((s) => s.status === "offline" || s.status === "error");
  const scanDuration = scanStartTime && scanEndTime ? `${Math.round((scanEndTime.getTime() - scanStartTime.getTime()) / 1e3)}s` : "Unknown";
  let report = `# ClaudeCode Relay Discovery Report

**Generated:** ${now.toISOString()}  
**Base Domain:** ${baseDomain}  
**Scan Duration:** ${scanDuration}  
**Total Discovered:** ${discoveredServers.length}  
**Online:** ${onlineServers.length}  
**Offline:** ${offlineServers.length}  

`;
  if (selectedServer) {
    report += `## Selected Relay

**Host:** ${selectedServer.host}${selectedServer.insecure ? " \u{1F513} (INSECURE)" : ""}  
**Description:** ${((_a = selectedServer.info) == null ? void 0 : _a.description) || "N/A"}  
**Status:** ${selectedServer.status}  
`;
    if (selectedServer.metrics) {
      report += `**Latency:** ${selectedServer.metrics.latency}ms  
**Last Checked:** ${selectedServer.metrics.lastChecked.toISOString()}  
`;
    }
    if ((_b = selectedServer.info) == null ? void 0 : _b.attributes) {
      const attrs = selectedServer.info.attributes;
      if (attrs.location) report += `**Location:** ${attrs.location}  
`;
      if (attrs.region) report += `**Region:** ${attrs.region}  
`;
      if (attrs.provider) report += `**Provider:** ${attrs.provider}  
`;
      if (attrs.capacity) report += `**Capacity:** ${attrs.capacity}  
`;
    }
    report += "\n";
  }
  if (onlineServers.length > 0) {
    report += `## Online Relays (${onlineServers.length})

| Relay | Latency | Status | Location | Description |
|-------|---------|--------|----------|-------------|
`;
    onlineServers.sort((a, b) => {
      var _a2, _b2;
      return (((_a2 = a.metrics) == null ? void 0 : _a2.latency) || 9999) - (((_b2 = b.metrics) == null ? void 0 : _b2.latency) || 9999);
    }).forEach((server) => {
      var _a2, _b2, _c, _d;
      const latency = ((_a2 = server.metrics) == null ? void 0 : _a2.latency) ? `${server.metrics.latency}ms` : "N/A";
      const location = ((_c = (_b2 = server.info) == null ? void 0 : _b2.attributes) == null ? void 0 : _c.location) || "N/A";
      const description = ((_d = server.info) == null ? void 0 : _d.description) || "N/A";
      const securityIcon = server.insecure ? " \u{1F513}" : "";
      report += `| ${server.host}${securityIcon} | ${latency} | ${server.status} | ${location} | ${description} |
`;
    });
    report += "\n";
  }
  if (offlineServers.length > 0) {
    report += `## Offline/Error Relays (${offlineServers.length})

| Relay | Status | Retry Count | Error |
|-------|--------|-------------|-------|
`;
    offlineServers.forEach((server) => {
      const retryCount = server.retryCount || 0;
      report += `| ${server.host} | ${server.status} | ${retryCount} | Network/DNS failure |
`;
    });
    report += "\n";
  }
  const peersFound = /* @__PURE__ */ new Set();
  onlineServers.forEach((server) => {
    var _a2;
    if ((_a2 = server.info) == null ? void 0 : _a2.peers) {
      server.info.peers.forEach((peer) => peersFound.add(peer));
    }
  });
  if (peersFound.size > 0) {
    report += `## Discovered Peers (${peersFound.size})

`;
    Array.from(peersFound).sort().forEach((peer) => {
      report += `- ${peer}
`;
    });
    report += "\n";
  }
  if (onlineServers.length > 0) {
    const latencies = onlineServers.filter((s) => {
      var _a2;
      return (_a2 = s.metrics) == null ? void 0 : _a2.latency;
    }).map((s) => s.metrics.latency).sort((a, b) => a - b);
    if (latencies.length > 0) {
      const avg = Math.round(latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length);
      const median = latencies[Math.floor(latencies.length / 2)];
      const min = latencies[0];
      const max = latencies[latencies.length - 1];
      report += `## Performance Summary

**Average Latency:** ${avg}ms  
**Median Latency:** ${median}ms  
**Best Latency:** ${min}ms  
**Worst Latency:** ${max}ms  

`;
    }
  }
  report += `## Switching Relays Later

To switch to a different relay from this list, you can run:
\`\`\`bash
claude --pick-relay <relay>
\`\`\`

Replace \`<relay>\` with any relay from the tables above.

---
*Generated by ClaudeCode Relay Selector v1.0.0*
`;
  return report;
}
function saveReport(report) {
  const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-").slice(0, 19);
  const filename = `claudecode-relay-results-${timestamp}.md`;
  const filepath = join2(homedir2(), filename);
  writeFileSync2(filepath, report, "utf8");
  return filepath;
}

// src/components/App.tsx
import { jsx as jsx7, jsxs as jsxs7 } from "react/jsx-runtime";
var App = ({ config: config2, onExit }) => {
  var _a, _b;
  const [currentScreen, setCurrentScreen] = useState6("discovery");
  const [discoveredServers, setDiscoveredServers] = useState6([]);
  const [selectedServer, setSelectedServer] = useState6(null);
  const [finalServers, setFinalServers] = useState6([]);
  const [retryCount, setRetryCount] = useState6(0);
  const [reportPath, setReportPath] = useState6(null);
  const [scanStartTime, setScanStartTime] = useState6(null);
  const [scanEndTime, setScanEndTime] = useState6(null);
  const handleDiscoveryComplete = (servers) => {
    setDiscoveredServers(servers);
    setScanEndTime(/* @__PURE__ */ new Date());
    const onlineServers = servers.filter((s) => s.status === "online");
    if (onlineServers.length === 0) {
      setCurrentScreen("countdown-retry");
    } else {
      setCurrentScreen("testing");
    }
  };
  const handleTestingComplete = useCallback((rankedServers) => {
    setFinalServers(rankedServers);
    const onlineServers = rankedServers.filter((s) => s.status === "online" && s.metrics);
    if (onlineServers.length === 0) {
      setCurrentScreen("countdown-retry");
    } else if (onlineServers.length === 1) {
      const selectedRelay = onlineServers[0];
      setSelectedServer(selectedRelay);
      writeRelayResult(selectedRelay);
      saveReportForServers(rankedServers, selectedRelay);
      setCurrentScreen("result");
    } else {
      setCurrentScreen("selection");
    }
  }, [config2.baseDomain, scanStartTime, scanEndTime]);
  const saveReportForServers = (servers, selectedServer2) => {
    try {
      const report = generateReport({
        baseDomain: config2.baseDomain,
        selectedServer: selectedServer2,
        discoveredServers: servers,
        scanStartTime,
        scanEndTime
      });
      const filepath = saveReport(report);
      setReportPath(filepath);
    } catch (error) {
      console.error("Failed to save report:", error);
    }
  };
  const handleServerSelect = (server) => {
    if (server.insecure) {
      setSelectedServer(server);
      setCurrentScreen("insecure-warning");
    } else {
      setSelectedServer(server);
      writeRelayResult(server);
      saveReportForServers(finalServers, server);
      setCurrentScreen("result");
    }
  };
  const handleInsecureConfirm = (server) => {
    setSelectedServer(server);
    writeRelayResult(server);
    saveReportForServers(finalServers, server);
    setCurrentScreen("result");
  };
  const handleInsecureCancel = () => {
    setCurrentScreen("selection");
  };
  const handleBack = () => {
    setCurrentScreen("discovery");
    setDiscoveredServers([]);
  };
  const handleRetry = () => {
    setRetryCount((prev) => prev + 1);
    setCurrentScreen("discovery");
    setDiscoveredServers([]);
    setFinalServers([]);
    setSelectedServer(null);
    setReportPath(null);
    setScanStartTime(null);
    setScanEndTime(null);
  };
  const handleRetryCancel = () => {
    onExit(void 0);
  };
  const handleExit2 = () => {
    onExit(selectedServer || void 0);
  };
  if (currentScreen === "discovery") {
    return /* @__PURE__ */ jsx7(
      DiscoveryScreen,
      {
        baseDomain: config2.baseDomain,
        onComplete: handleDiscoveryComplete,
        onStart: setScanStartTime
      },
      retryCount
    );
  }
  if (currentScreen === "testing") {
    return /* @__PURE__ */ jsx7(
      TestingScreen,
      {
        servers: discoveredServers,
        onComplete: handleTestingComplete
      }
    );
  }
  if (currentScreen === "selection") {
    return /* @__PURE__ */ jsx7(
      SelectionScreen,
      {
        servers: finalServers,
        onSelect: handleServerSelect,
        onBack: handleBack,
        baseDomain: config2.baseDomain
      }
    );
  }
  if (currentScreen === "insecure-warning" && selectedServer) {
    return /* @__PURE__ */ jsx7(
      InsecureWarningScreen,
      {
        server: selectedServer,
        onConfirm: handleInsecureConfirm,
        onCancel: handleInsecureCancel
      }
    );
  }
  if (currentScreen === "countdown-retry") {
    return /* @__PURE__ */ jsx7(
      CountdownRetryScreen,
      {
        onRetry: handleRetry,
        onCancel: handleRetryCancel,
        discoveredCount: finalServers.length || discoveredServers.length,
        countdownSeconds: 6
      }
    );
  }
  return /* @__PURE__ */ jsxs7(Box7, { flexDirection: "column", padding: 1, children: [
    /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsx7(Text7, { bold: true, color: "green", children: "\u2705 Relay Selection Complete" }) }),
    selectedServer ? /* @__PURE__ */ jsxs7(Box7, { flexDirection: "column", children: [
      /* @__PURE__ */ jsxs7(Box7, { marginBottom: 1, children: [
        /* @__PURE__ */ jsxs7(Text7, { color: "cyan", bold: true, children: [
          "Selected Relay: ",
          selectedServer.host
        ] }),
        selectedServer.insecure && /* @__PURE__ */ jsx7(Text7, { color: "red", children: " \u{1F513} (INSECURE)" })
      ] }),
      /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsx7(Text7, { children: (_a = selectedServer.info) == null ? void 0 : _a.description }) }),
      selectedServer.metrics && /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsxs7(Text7, { color: "green", children: [
        "Performance: ",
        selectedServer.metrics.latency,
        "ms latency"
      ] }) }),
      ((_b = selectedServer.info) == null ? void 0 : _b.attributes.location) && /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsxs7(Text7, { color: "gray", children: [
        "Location: ",
        selectedServer.info.attributes.location
      ] }) }),
      reportPath && /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsxs7(Text7, { color: "yellow", children: [
        "\u{1F4C4} Report saved to: ",
        reportPath
      ] }) }),
      reportPath && /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsx7(Text7, { color: "gray", children: "\u{1F4A1} To switch to a different relay later, check the file and run: claude --pick-relay <relay>" }) })
    ] }) : /* @__PURE__ */ jsxs7(Box7, { flexDirection: "column", children: [
      /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsx7(Text7, { color: "red", children: "\u274C No relay servers found" }) }),
      /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsxs7(Text7, { children: [
        "Discovered ",
        finalServers.length || discoveredServers.length,
        " servers, none are currently reachable."
      ] }) }),
      retryCount > 0 && /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsxs7(Text7, { color: "gray", children: [
        "Attempted ",
        retryCount + 1,
        " time",
        retryCount !== 0 ? "s" : "",
        " total."
      ] }) }),
      /* @__PURE__ */ jsx7(Box7, { marginBottom: 1, children: /* @__PURE__ */ jsx7(Text7, { color: "gray", children: "This may be due to network issues, DNS problems, or all relay servers being temporarily offline." }) })
    ] })
  ] });
};

// src/index.tsx
import { jsx as jsx8 } from "react/jsx-runtime";
var program = new Command();
program.name("relay-selector").description("TUI for discovering and selecting optimal relay servers").option("-d, --domain <domain>", "Base domain to scan", "gaccode.com").parse();
var options = program.opts();
var config = {
  baseDomain: options.domain,
  timeout: 3e3,
  maxRetries: 3,
  testEndpoint: "/relay/info"
};
var handleExit = (selectedServer) => {
  var _a;
  if (selectedServer) {
    console.log(`
\u{1F680} Selected relay: ${selectedServer.host}`);
    if (selectedServer.metrics) {
      console.log(`\u26A1 Latency: ${selectedServer.metrics.latency}ms`);
    }
    if ((_a = selectedServer.info) == null ? void 0 : _a.attributes.location) {
      console.log(`\u{1F4CD} Location: ${selectedServer.info.attributes.location}`);
    }
  } else {
    console.log("\n\u274C No relay server selected");
  }
  process.exit(selectedServer ? 0 : 1);
};
process.on("SIGINT", () => {
  console.log("\n\n\u{1F6D1} Discovery interrupted by user");
  process.exit(130);
});
process.on("SIGTERM", () => {
  console.log("\n\n\u{1F6D1} Discovery terminated");
  process.exit(143);
});
render(/* @__PURE__ */ jsx8(App, { config, onExit: handleExit }));
