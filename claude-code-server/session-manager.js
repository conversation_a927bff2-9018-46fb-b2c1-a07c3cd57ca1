const ClaudeInstance = require('./claude-instance');
const crypto = require('crypto');

class SessionManager {
  constructor(maxSessions = 4) {
    this.maxSessions = maxSessions;
    this.sessions = new Map(); // sessionKey -> session
    this.userSessions = new Map(); // userKey -> sessionKey
    this.wsSessions = new Map(); // ws -> sessionKey
  }

  async getOrCreateSession(userKey, ws) {
    // 检查用户是否已有会话
    if (this.userSessions.has(userKey)) {
      const sessionKey = this.userSessions.get(userKey);
      const session = this.sessions.get(sessionKey);
      
      if (session && session.isActive) {
        // 更新WebSocket连接
        this.wsSessions.set(ws, sessionKey);
        session.ws = ws;
        session.lastActivity = Date.now();
        return session;
      }
    }

    // 检查是否达到最大会话数
    if (this.sessions.size >= this.maxSessions) {
      // 尝试清理不活跃的会话
      this.cleanupInactiveSessions();
      
      if (this.sessions.size >= this.maxSessions) {
        return null; // 无可用会话
      }
    }

    // 创建新会话
    const sessionKey = this.generateSessionKey();
    const session = await this.createSession(sessionKey, userKey, ws);
    
    this.sessions.set(sessionKey, session);
    this.userSessions.set(userKey, sessionKey);
    this.wsSessions.set(ws, sessionKey);
    
    console.log(`Created new session ${sessionKey} for user ${userKey}`);
    return session;
  }

  async createSession(sessionKey, userKey, ws) {
    const claudeInstance = new ClaudeInstance({
      sessionKey,
      userKey
    });

    await claudeInstance.initialize();

    return {
      key: sessionKey,
      id: sessionKey.substring(0, 8),
      userKey,
      ws,
      claudeInstance,
      isActive: true,
      createdAt: Date.now(),
      lastActivity: Date.now()
    };
  }

  getSession(sessionKey) {
    const session = this.sessions.get(sessionKey);
    if (session) {
      session.lastActivity = Date.now();
    }
    return session;
  }

  releaseSession(sessionKey) {
    const session = this.sessions.get(sessionKey);
    if (!session) return;

    console.log(`Releasing session ${sessionKey}`);
    
    // 清理Claude实例
    if (session.claudeInstance) {
      session.claudeInstance.cleanup();
    }

    // 清理映射
    this.sessions.delete(sessionKey);
    this.userSessions.delete(session.userKey);
    
    // 清理WebSocket映射
    for (const [ws, key] of this.wsSessions.entries()) {
      if (key === sessionKey) {
        this.wsSessions.delete(ws);
        break;
      }
    }
  }

  releaseSessionByWs(ws) {
    const sessionKey = this.wsSessions.get(ws);
    if (sessionKey) {
      const session = this.sessions.get(sessionKey);
      if (session) {
        session.isActive = false;
        // 不立即删除，给用户重连的机会
        setTimeout(() => {
          if (this.sessions.has(sessionKey) && !this.sessions.get(sessionKey).isActive) {
            this.releaseSession(sessionKey);
          }
        }, 30000); // 30秒后清理
      }
      this.wsSessions.delete(ws);
    }
  }

  cleanupInactiveSessions() {
    const now = Date.now();
    const inactiveThreshold = 30 * 60 * 1000; // 30分钟

    for (const [sessionKey, session] of this.sessions.entries()) {
      if (!session.isActive || (now - session.lastActivity) > inactiveThreshold) {
        this.releaseSession(sessionKey);
      }
    }
  }

  generateSessionKey() {
    return crypto.randomBytes(16).toString('hex');
  }

  getSessionCount() {
    return this.sessions.size;
  }

  getSessionsInfo() {
    const sessions = [];
    for (const [key, session] of this.sessions.entries()) {
      sessions.push({
        key: key.substring(0, 8),
        userKey: session.userKey,
        isActive: session.isActive,
        createdAt: new Date(session.createdAt).toISOString(),
        lastActivity: new Date(session.lastActivity).toISOString()
      });
    }
    return sessions;
  }

  // 定期清理不活跃会话
  startCleanupTimer() {
    setInterval(() => {
      this.cleanupInactiveSessions();
    }, 5 * 60 * 1000); // 每5分钟清理一次
  }
}

module.exports = SessionManager;
