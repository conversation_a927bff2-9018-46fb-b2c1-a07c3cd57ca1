# Claude Code Server

多用户共享Claude Code服务器，支持多个用户同时使用一个Claude Code账号。

## 功能特性

- 支持最多4个并发用户会话
- 完整的工具调用支持（文件读写、代码编辑等）
- 流式响应
- 会话管理和自动清理
- WebSocket实时通信
- 安全的API密钥认证

## 安装部署

### 1. 服务器端部署

```bash
# 克隆或复制服务器代码
cd claude-code-server

# 安装依赖
npm install

# 配置环境变量
export ANTHROPIC_API_KEY="your-anthropic-api-key"
export CLAUDE_CODE_PATH="/path/to/claude-code/cli.js"

# 修改配置文件
vim config.json

# 启动服务器
npm start
```

### 2. 客户端配置

```bash
# 进入客户端目录
cd ccai

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
vim .env

# 设置以下变量：
# CLAUDE_CODE_USE_REMOTE=true
# CLAUDE_CODE_SERVER_URL=ws://your-server.com:3000
# CLAUDE_CODE_API_KEY=your-api-key
# CLAUDE_CODE_USER_KEY=user1
```

## 使用方法

### 服务器端

```bash
# 启动服务器
npm start

# 开发模式（自动重启）
npm run dev

# 查看会话状态
curl http://localhost:3000/sessions

# 健康检查
curl http://localhost:3000/health
```

### 客户端

```javascript
import { query } from './sdk.mjs';

// 使用远程服务器
const response = query({
  prompt: "帮我分析这个代码库",
  options: {
    useRemoteServer: true,
    cwd: "/path/to/your/project"
  }
});

for await (const message of response) {
  console.log(message);
}
```

或者通过命令行：

```bash
# 设置环境变量后直接使用
export CLAUDE_CODE_USE_REMOTE=true
export CLAUDE_CODE_SERVER_URL=ws://your-server.com:3000
export CLAUDE_CODE_API_KEY=your-api-key
export CLAUDE_CODE_USER_KEY=user1

# 运行Claude Code客户端
node cli.js
```

## 配置说明

### 服务器配置 (config.json)

```json
{
  "maxSessions": 4,           // 最大并发会话数
  "port": 3000,               // 服务器端口
  "validApiKeys": [...],      // 有效的API密钥列表
  "claudeCodePath": "claude", // Claude Code CLI路径
  "sessionTimeout": 1800000,  // 会话超时时间(毫秒)
  "cleanupInterval": 300000   // 清理间隔(毫秒)
}
```

### 客户端环境变量

- `CLAUDE_CODE_USE_REMOTE`: 启用远程模式
- `CLAUDE_CODE_SERVER_URL`: 服务器WebSocket地址
- `CLAUDE_CODE_API_KEY`: 用户API密钥
- `CLAUDE_CODE_USER_KEY`: 用户标识符
- `DEBUG`: 启用调试日志

## 架构说明

```
客户端1 ──┐
客户端2 ──┼── WebSocket ──┐
客户端3 ──┤               ├── 服务器 ──┐
客户端4 ──┘               │           ├── Claude实例1
                          │           ├── Claude实例2  
                          │           ├── Claude实例3
                          │           └── Claude实例4
```

每个客户端通过WebSocket连接到服务器，服务器为每个用户维护独立的Claude Code实例。

## 支持的工具

- `view`: 查看文件内容
- `str-replace-editor`: 编辑文件
- `save-file`: 保存新文件
- `remove-files`: 删除文件
- `launch-process`: 启动进程
- `read-process`: 读取进程输出
- `write-process`: 向进程写入
- `kill-process`: 终止进程

## 安全注意事项

1. 使用强API密钥
2. 限制服务器访问权限
3. 配置防火墙规则
4. 定期更新依赖
5. 监控服务器资源使用

## 故障排除

### 连接问题
- 检查服务器是否运行
- 验证WebSocket URL
- 确认API密钥正确

### 会话问题
- 检查是否达到最大会话数
- 查看服务器日志
- 重启客户端重新连接

### 工具调用问题
- 确认文件路径权限
- 检查工作目录设置
- 查看错误日志
