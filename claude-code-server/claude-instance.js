const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class ClaudeInstance {
  constructor(options) {
    this.sessionKey = options.sessionKey;
    this.userKey = options.userKey;
    this.process = null;
    this.isInitialized = false;
    this.onMessage = null;
    this.messageQueue = [];
    this.currentQuery = null;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // 启动Claude Code进程
      await this.startClaudeProcess();
      this.isInitialized = true;
      console.log(`Claude instance initialized for session ${this.sessionKey}`);
    } catch (error) {
      console.error(`Failed to initialize Claude instance: ${error.message}`);
      throw error;
    }
  }

  async startClaudeProcess() {
    return new Promise((resolve, reject) => {
      // Claude Code CLI路径
      const claudeCodePath = process.env.CLAUDE_CODE_PATH || 'claude';
      
      // 启动参数
      const args = [
        '--output-format', 'stream-json',
        '--verbose',
        '--input-format', 'stream-json'
      ];

      // 启动进程
      this.process = spawn(claudeCodePath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          CLAUDE_CODE_SESSION: this.sessionKey
        }
      });

      // 处理标准输出
      this.process.stdout.on('data', (data) => {
        this.handleClaudeOutput(data);
      });

      // 处理标准错误
      this.process.stderr.on('data', (data) => {
        console.error(`Claude stderr [${this.sessionKey}]:`, data.toString());
      });

      // 处理进程退出
      this.process.on('exit', (code) => {
        console.log(`Claude process exited with code ${code} for session ${this.sessionKey}`);
        this.isInitialized = false;
      });

      // 处理进程错误
      this.process.on('error', (error) => {
        console.error(`Claude process error for session ${this.sessionKey}:`, error);
        reject(error);
      });

      // 等待进程启动
      setTimeout(() => {
        if (this.process && !this.process.killed) {
          resolve();
        } else {
          reject(new Error('Failed to start Claude process'));
        }
      }, 2000);
    });
  }

  handleClaudeOutput(data) {
    const output = data.toString();
    const lines = output.split('\n').filter(line => line.trim());

    for (const line of lines) {
      try {
        const message = JSON.parse(line);
        this.processClaudeMessage(message);
      } catch (error) {
        // 可能是非JSON输出，直接转发
        if (this.onMessage) {
          this.onMessage({
            type: 'raw_output',
            content: line
          });
        }
      }
    }
  }

  processClaudeMessage(message) {
    // 处理工具调用
    if (message.type === 'assistant' && message.message && message.message.content) {
      for (const content of message.message.content) {
        if (content.type === 'tool_use') {
          this.handleToolUse(content, message);
          return;
        }
      }
    }

    // 转发其他消息
    if (this.onMessage) {
      this.onMessage(message);
    }
  }

  async handleToolUse(toolUse, originalMessage) {
    const { name, input, id } = toolUse;
    
    // 转发工具调用请求到客户端
    if (this.onMessage) {
      this.onMessage({
        type: 'tool_use_request',
        tool_name: name,
        tool_input: input,
        tool_use_id: id,
        original_message: originalMessage
      });
    }
  }

  async query(queryData) {
    if (!this.isInitialized) {
      throw new Error('Claude instance not initialized');
    }

    if (!this.process || this.process.killed) {
      throw new Error('Claude process not running');
    }

    try {
      // 构造查询消息
      const message = {
        type: 'user',
        message: {
          role: 'user',
          content: queryData.prompt || queryData.message || queryData
        },
        parent_tool_use_id: null,
        session_id: this.sessionKey
      };

      // 发送到Claude进程
      const messageStr = JSON.stringify(message) + '\n';
      this.process.stdin.write(messageStr);
      
      this.currentQuery = queryData;
      
    } catch (error) {
      console.error(`Error sending query to Claude: ${error.message}`);
      throw error;
    }
  }

  async sendToolResult(toolUseId, result, isError = false) {
    if (!this.isInitialized || !this.process || this.process.killed) {
      throw new Error('Claude instance not available');
    }

    try {
      const message = {
        type: 'user',
        message: {
          role: 'user',
          content: [
            {
              type: 'tool_result',
              tool_use_id: toolUseId,
              content: isError ? `Error: ${result}` : result,
              is_error: isError
            }
          ]
        },
        parent_tool_use_id: toolUseId,
        session_id: this.sessionKey
      };

      const messageStr = JSON.stringify(message) + '\n';
      this.process.stdin.write(messageStr);
      
    } catch (error) {
      console.error(`Error sending tool result: ${error.message}`);
      throw error;
    }
  }

  cleanup() {
    if (this.process && !this.process.killed) {
      console.log(`Cleaning up Claude instance for session ${this.sessionKey}`);
      this.process.kill('SIGTERM');
      
      // 强制杀死进程（如果需要）
      setTimeout(() => {
        if (this.process && !this.process.killed) {
          this.process.kill('SIGKILL');
        }
      }, 5000);
    }
    
    this.isInitialized = false;
    this.onMessage = null;
  }

  isHealthy() {
    return this.isInitialized && this.process && !this.process.killed;
  }
}

module.exports = ClaudeInstance;
