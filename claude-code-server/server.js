#!/usr/bin/env node

const WebSocket = require('ws');
const express = require('express');
const http = require('http');
const SessionManager = require('./session-manager');
const ClaudeInstance = require('./claude-instance');
const FileProxy = require('./file-proxy');
const crypto = require('crypto');

class ClaudeCodeServer {
  constructor(config) {
    this.config = config;
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = new WebSocket.Server({ server: this.server });
    this.sessionManager = new SessionManager(config.maxSessions || 4);
    this.fileProxy = new FileProxy();
    
    this.setupRoutes();
    this.setupWebSocket();
  }

  setupRoutes() {
    this.app.use(express.json());
    
    // 健康检查
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'ok', 
        sessions: this.sessionManager.getSessionCount(),
        maxSessions: this.config.maxSessions 
      });
    });

    // 获取会话信息
    this.app.get('/sessions', (req, res) => {
      res.json(this.sessionManager.getSessionsInfo());
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws, req) => {
      console.log('New WebSocket connection');
      
      ws.on('message', async (data) => {
        try {
          const message = JSON.parse(data.toString());
          await this.handleMessage(ws, message);
        } catch (error) {
          console.error('Error handling message:', error);
          ws.send(JSON.stringify({
            type: 'error',
            error: error.message
          }));
        }
      });

      ws.on('close', () => {
        console.log('WebSocket connection closed');
        this.sessionManager.releaseSessionByWs(ws);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
      });
    });
  }

  async handleMessage(ws, message) {
    const { type, sessionKey, data } = message;

    switch (type) {
      case 'auth':
        await this.handleAuth(ws, data);
        break;
        
      case 'query':
        await this.handleQuery(ws, sessionKey, data);
        break;
        
      case 'file_operation':
        await this.handleFileOperation(ws, sessionKey, data);
        break;
        
      case 'ping':
        ws.send(JSON.stringify({ type: 'pong' }));
        break;
        
      default:
        ws.send(JSON.stringify({
          type: 'error',
          error: `Unknown message type: ${type}`
        }));
    }
  }

  async handleAuth(ws, authData) {
    const { apiKey, userKey } = authData;
    
    // 验证API密钥
    if (!this.validateApiKey(apiKey)) {
      ws.send(JSON.stringify({
        type: 'auth_error',
        error: 'Invalid API key'
      }));
      return;
    }

    // 分配或获取会话
    const session = await this.sessionManager.getOrCreateSession(userKey, ws);
    
    if (!session) {
      ws.send(JSON.stringify({
        type: 'auth_error',
        error: 'No available sessions'
      }));
      return;
    }

    ws.send(JSON.stringify({
      type: 'auth_success',
      sessionKey: session.key,
      sessionId: session.id
    }));
  }

  async handleQuery(ws, sessionKey, queryData) {
    const session = this.sessionManager.getSession(sessionKey);
    
    if (!session) {
      ws.send(JSON.stringify({
        type: 'error',
        error: 'Invalid session'
      }));
      return;
    }

    // 转发查询到Claude实例
    try {
      const claudeInstance = session.claudeInstance;
      
      // 设置流式响应处理
      claudeInstance.onMessage = (message) => {
        ws.send(JSON.stringify({
          type: 'claude_message',
          sessionKey,
          data: message
        }));
      };

      // 发送查询
      await claudeInstance.query(queryData);
      
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        sessionKey,
        error: error.message
      }));
    }
  }

  async handleFileOperation(ws, sessionKey, fileOpData) {
    const session = this.sessionManager.getSession(sessionKey);
    
    if (!session) {
      ws.send(JSON.stringify({
        type: 'error',
        error: 'Invalid session'
      }));
      return;
    }

    try {
      const result = await this.fileProxy.handleOperation(fileOpData);
      
      ws.send(JSON.stringify({
        type: 'file_operation_result',
        sessionKey,
        data: result
      }));
      
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        sessionKey,
        error: error.message
      }));
    }
  }

  validateApiKey(apiKey) {
    // 实现API密钥验证逻辑
    return this.config.validApiKeys.includes(apiKey);
  }

  start(port = 3000) {
    this.server.listen(port, () => {
      console.log(`Claude Code Server running on port ${port}`);
      console.log(`Max sessions: ${this.config.maxSessions}`);
    });
  }
}

// 配置
const config = {
  maxSessions: 4,
  validApiKeys: [
    'your-api-key-1',
    'your-api-key-2',
    // 添加更多API密钥
  ],
  claudeCodePath: '/path/to/claude-code/cli.js'
};

// 启动服务器
const server = new ClaudeCodeServer(config);
server.start(process.env.PORT || 3000);

module.exports = ClaudeCodeServer;
