const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

class FileProxy {
  constructor() {
    this.allowedOperations = [
      'read_file',
      'write_file',
      'list_directory',
      'create_directory',
      'delete_file',
      'move_file',
      'search_files',
      'run_command'
    ];
  }

  async handleOperation(operation) {
    const { type, params, clientPath } = operation;

    if (!this.allowedOperations.includes(type)) {
      throw new Error(`Operation ${type} not allowed`);
    }

    // 安全检查：确保路径在允许的范围内
    if (params.path && !this.isPathSafe(params.path, clientPath)) {
      throw new Error('Path access denied');
    }

    switch (type) {
      case 'read_file':
        return await this.readFile(params, clientPath);
      case 'write_file':
        return await this.writeFile(params, clientPath);
      case 'list_directory':
        return await this.listDirectory(params, clientPath);
      case 'create_directory':
        return await this.createDirectory(params, clientPath);
      case 'delete_file':
        return await this.deleteFile(params, clientPath);
      case 'move_file':
        return await this.moveFile(params, clientPath);
      case 'search_files':
        return await this.searchFiles(params, clientPath);
      case 'run_command':
        return await this.runCommand(params, clientPath);
      default:
        throw new Error(`Unknown operation: ${type}`);
    }
  }

  isPathSafe(targetPath, basePath) {
    // 确保路径在客户端指定的基础路径内
    const resolvedTarget = path.resolve(basePath, targetPath);
    const resolvedBase = path.resolve(basePath);
    return resolvedTarget.startsWith(resolvedBase);
  }

  async readFile(params, clientPath) {
    const { path: filePath, encoding = 'utf8' } = params;
    const fullPath = path.resolve(clientPath, filePath);
    
    try {
      const content = await fs.readFile(fullPath, encoding);
      return {
        success: true,
        content,
        path: filePath,
        size: content.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: filePath
      };
    }
  }

  async writeFile(params, clientPath) {
    const { path: filePath, content, encoding = 'utf8' } = params;
    const fullPath = path.resolve(clientPath, filePath);
    
    try {
      // 确保目录存在
      const dir = path.dirname(fullPath);
      await fs.mkdir(dir, { recursive: true });
      
      await fs.writeFile(fullPath, content, encoding);
      return {
        success: true,
        path: filePath,
        size: content.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: filePath
      };
    }
  }

  async listDirectory(params, clientPath) {
    const { path: dirPath = '.', recursive = false } = params;
    const fullPath = path.resolve(clientPath, dirPath);
    
    try {
      const items = [];
      
      if (recursive) {
        await this.listDirectoryRecursive(fullPath, items, dirPath);
      } else {
        const entries = await fs.readdir(fullPath, { withFileTypes: true });
        for (const entry of entries) {
          const itemPath = path.join(dirPath, entry.name);
          items.push({
            name: entry.name,
            path: itemPath,
            type: entry.isDirectory() ? 'directory' : 'file',
            isDirectory: entry.isDirectory(),
            isFile: entry.isFile()
          });
        }
      }
      
      return {
        success: true,
        path: dirPath,
        items
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: dirPath
      };
    }
  }

  async listDirectoryRecursive(fullPath, items, relativePath) {
    const entries = await fs.readdir(fullPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const itemPath = path.join(relativePath, entry.name);
      const itemFullPath = path.join(fullPath, entry.name);
      
      items.push({
        name: entry.name,
        path: itemPath,
        type: entry.isDirectory() ? 'directory' : 'file',
        isDirectory: entry.isDirectory(),
        isFile: entry.isFile()
      });
      
      if (entry.isDirectory()) {
        await this.listDirectoryRecursive(itemFullPath, items, itemPath);
      }
    }
  }

  async createDirectory(params, clientPath) {
    const { path: dirPath } = params;
    const fullPath = path.resolve(clientPath, dirPath);
    
    try {
      await fs.mkdir(fullPath, { recursive: true });
      return {
        success: true,
        path: dirPath
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: dirPath
      };
    }
  }

  async deleteFile(params, clientPath) {
    const { path: filePath } = params;
    const fullPath = path.resolve(clientPath, filePath);
    
    try {
      const stats = await fs.stat(fullPath);
      if (stats.isDirectory()) {
        await fs.rmdir(fullPath, { recursive: true });
      } else {
        await fs.unlink(fullPath);
      }
      
      return {
        success: true,
        path: filePath
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: filePath
      };
    }
  }

  async moveFile(params, clientPath) {
    const { from, to } = params;
    const fromPath = path.resolve(clientPath, from);
    const toPath = path.resolve(clientPath, to);
    
    try {
      await fs.rename(fromPath, toPath);
      return {
        success: true,
        from,
        to
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        from,
        to
      };
    }
  }

  async searchFiles(params, clientPath) {
    const { pattern, path: searchPath = '.', fileTypes = [] } = params;
    const fullPath = path.resolve(clientPath, searchPath);
    
    try {
      const results = [];
      await this.searchInDirectory(fullPath, pattern, fileTypes, results, searchPath);
      
      return {
        success: true,
        pattern,
        path: searchPath,
        results
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        pattern,
        path: searchPath
      };
    }
  }

  async searchInDirectory(fullPath, pattern, fileTypes, results, relativePath) {
    const entries = await fs.readdir(fullPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const itemPath = path.join(relativePath, entry.name);
      const itemFullPath = path.join(fullPath, entry.name);
      
      if (entry.isFile()) {
        const ext = path.extname(entry.name);
        if (fileTypes.length === 0 || fileTypes.includes(ext)) {
          if (entry.name.includes(pattern)) {
            results.push({
              name: entry.name,
              path: itemPath,
              type: 'file'
            });
          }
        }
      } else if (entry.isDirectory() && !entry.name.startsWith('.')) {
        await this.searchInDirectory(itemFullPath, pattern, fileTypes, results, itemPath);
      }
    }
  }

  async runCommand(params, clientPath) {
    const { command, args = [], timeout = 30000 } = params;
    
    return new Promise((resolve) => {
      const process = spawn(command, args, {
        cwd: clientPath,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';
      let timedOut = false;

      const timer = setTimeout(() => {
        timedOut = true;
        process.kill('SIGTERM');
      }, timeout);

      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        clearTimeout(timer);
        resolve({
          success: !timedOut && code === 0,
          code,
          stdout,
          stderr,
          timedOut,
          command: `${command} ${args.join(' ')}`
        });
      });

      process.on('error', (error) => {
        clearTimeout(timer);
        resolve({
          success: false,
          error: error.message,
          command: `${command} ${args.join(' ')}`
        });
      });
    });
  }
}

module.exports = FileProxy;
