// (c) Anthropic PBC. All rights reserved. Use is subject to Anthropic's Commercial Terms of Service (https://www.anthropic.com/legal/commercial-terms).

// Version: 1.0.44

// src/entrypoints/sdk.ts
import { spawn } from "child_process";
import { join } from "path";
import { fileURLToPath } from "url";
import { createInterface } from "readline";
import { existsSync } from "fs";
import WebSocket from "ws";
import { promises as fs } from "fs";

var __filename2 = fileURLToPath(import.meta.url);
var __dirname2 = join(__filename2, "..");

// 远程服务器配置
const REMOTE_SERVER_CONFIG = {
  url: process.env.CLAUDE_CODE_SERVER_URL || 'ws://localhost:3000',
  apiKey: process.env.CLAUDE_CODE_API_KEY,
  userKey: process.env.CLAUDE_CODE_USER_KEY || 'default-user',
  timeout: 30000
};

async function* query({
  prompt,
  options: {
    abortController = new AbortController,
    allowedTools = [],
    appendSystemPrompt,
    customSystemPrompt,
    cwd = process.cwd(),
    disallowedTools = [],
    executable = isRunningWithBun() ? "bun" : "node",
    executableArgs = [],
    maxTurns,
    mcpServers,
    pathToClaudeCodeExecutable = join(__dirname2, "cli.js"),
    permissionMode = "default",
    permissionPromptToolName,
    continue: continueConversation,
    resume,
    model,
    fallbackModel,
    strictMcpConfig,
    // 新增远程服务器选项
    useRemoteServer = process.env.CLAUDE_CODE_USE_REMOTE === 'true',
    serverUrl = REMOTE_SERVER_CONFIG.url,
    apiKey = REMOTE_SERVER_CONFIG.apiKey,
    userKey = REMOTE_SERVER_CONFIG.userKey
  } = {}
}) {
  // 检查是否使用远程服务器
  if (useRemoteServer) {
    if (!apiKey) {
      throw new Error("API key is required for remote server connection. Set CLAUDE_CODE_API_KEY environment variable.");
    }
    yield* queryRemoteServer({
      prompt,
      serverUrl,
      apiKey,
      userKey,
      cwd,
      options: {
        customSystemPrompt,
        appendSystemPrompt,
        maxTurns,
        model,
        permissionMode,
        allowedTools,
        disallowedTools,
        mcpServers,
        strictMcpConfig,
        fallbackModel
      },
      abortController
    });
    return;
  }

  if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
    process.env.CLAUDE_CODE_ENTRYPOINT = "sdk-ts";
  }
  const args = ["--output-format", "stream-json", "--verbose"];
  if (customSystemPrompt)
    args.push("--system-prompt", customSystemPrompt);
  if (appendSystemPrompt)
    args.push("--append-system-prompt", appendSystemPrompt);
  if (maxTurns)
    args.push("--max-turns", maxTurns.toString());
  if (model)
    args.push("--model", model);
  if (permissionPromptToolName)
    args.push("--permission-prompt-tool", permissionPromptToolName);
  if (continueConversation)
    args.push("--continue");
  if (resume)
    args.push("--resume", resume);
  if (allowedTools.length > 0) {
    args.push("--allowedTools", allowedTools.join(","));
  }
  if (disallowedTools.length > 0) {
    args.push("--disallowedTools", disallowedTools.join(","));
  }
  if (mcpServers && Object.keys(mcpServers).length > 0) {
    args.push("--mcp-config", JSON.stringify({ mcpServers }));
  }
  if (strictMcpConfig) {
    args.push("--strict-mcp-config");
  }
  if (permissionMode !== "default") {
    args.push("--permission-mode", permissionMode);
  }
  if (fallbackModel) {
    if (model && fallbackModel === model) {
      throw new Error("Fallback model cannot be the same as the main model. Please specify a different model for fallbackModel option.");
    }
    args.push("--fallback-model", fallbackModel);
  }
  if (typeof prompt === "string") {
    args.push("--print", prompt.trim());
  } else {
    args.push("--input-format", "stream-json");
  }
  if (!existsSync(pathToClaudeCodeExecutable)) {
    throw new ReferenceError(`Claude Code executable not found at ${pathToClaudeCodeExecutable}. Is options.pathToClaudeCodeExecutable set?`);
  }
  logDebug(`Spawning Claude Code process: ${executable} ${[...executableArgs, pathToClaudeCodeExecutable, ...args].join(" ")}`);
  const child = spawn(executable, [...executableArgs, pathToClaudeCodeExecutable, ...args], {
    cwd,
    stdio: ["pipe", "pipe", "pipe"],
    signal: abortController.signal,
    env: {
      ...process.env
    }
  });
  if (typeof prompt === "string") {
    child.stdin.end();
  } else {
    streamToStdin(prompt, child.stdin, abortController);
  }
  if (process.env.DEBUG) {
    child.stderr.on("data", (data) => {
      console.error("Claude Code stderr:", data.toString());
    });
  }
  const cleanup = () => {
    if (!child.killed) {
      child.kill("SIGTERM");
    }
  };
  abortController.signal.addEventListener("abort", cleanup);
  process.on("exit", cleanup);
  try {
    let processError = null;
    child.on("error", (error) => {
      processError = new Error(`Failed to spawn Claude Code process: ${error.message}`);
    });
    const processExitPromise = new Promise((resolve, reject) => {
      child.on("close", (code) => {
        if (abortController.signal.aborted) {
          reject(new AbortError("Claude Code process aborted by user"));
        }
        if (code !== 0) {
          reject(new Error(`Claude Code process exited with code ${code}`));
        } else {
          resolve();
        }
      });
    });
    const rl = createInterface({ input: child.stdout });
    try {
      for await (const line of rl) {
        if (processError) {
          throw processError;
        }
        if (line.trim()) {
          yield JSON.parse(line);
        }
      }
    } finally {
      rl.close();
    }
    await processExitPromise;
  } finally {
    cleanup();
    abortController.signal.removeEventListener("abort", cleanup);
    if (process.env.CLAUDE_SDK_MCP_SERVERS) {
      delete process.env.CLAUDE_SDK_MCP_SERVERS;
    }
  }
}
async function streamToStdin(stream, stdin, abortController) {
  for await (const message of stream) {
    if (abortController.signal.aborted)
      break;
    stdin.write(JSON.stringify(message) + `
`);
  }
  stdin.end();
}
// 远程服务器查询函数
async function* queryRemoteServer({
  prompt,
  serverUrl,
  apiKey,
  userKey,
  cwd,
  options,
  abortController
}) {
  const ws = new WebSocket(serverUrl);
  let sessionKey = null;
  let isAuthenticated = false;

  const messageQueue = [];

  // 连接建立
  await new Promise((resolve, reject) => {
    ws.on('open', () => {
      logDebug('Connected to remote Claude Code server');
      resolve();
    });

    ws.on('error', (error) => {
      reject(new Error(`Failed to connect to server: ${error.message}`));
    });
  });

  // 消息处理
  ws.on('message', async (data) => {
    try {
      const message = JSON.parse(data.toString());
      await handleServerMessage(message);
    } catch (error) {
      console.error('Error handling server message:', error);
    }
  });

  async function handleServerMessage(message) {
    const { type } = message;

    switch (type) {
      case 'auth_success':
        sessionKey = message.sessionKey;
        isAuthenticated = true;
        logDebug(`Authenticated with session: ${sessionKey}`);

        // 发送查询
        await sendQuery();
        break;

      case 'auth_error':
        throw new Error(`Authentication failed: ${message.error}`);

      case 'claude_message':
        messageQueue.push(message.data);
        break;

      case 'tool_use_request':
        await handleToolUseRequest(message);
        break;

      case 'error':
        throw new Error(`Server error: ${message.error}`);

      default:
        logDebug(`Unknown message type: ${type}`);
    }
  }

  async function handleToolUseRequest(message) {
    const { tool_name, tool_input, tool_use_id } = message;

    try {
      let result;

      // 处理文件操作工具
      if (isFileOperation(tool_name)) {
        result = await handleFileOperation(tool_name, tool_input, cwd);
      } else {
        result = await handleOtherToolCall(tool_name, tool_input, cwd);
      }

      // 发送工具结果回服务器
      ws.send(JSON.stringify({
        type: 'tool_result',
        sessionKey,
        tool_use_id,
        result,
        is_error: false
      }));

    } catch (error) {
      // 发送错误结果
      ws.send(JSON.stringify({
        type: 'tool_result',
        sessionKey,
        tool_use_id,
        result: error.message,
        is_error: true
      }));
    }
  }

  async function sendQuery() {
    ws.send(JSON.stringify({
      type: 'query',
      sessionKey,
      data: {
        prompt: typeof prompt === 'string' ? prompt : prompt,
        options,
        cwd
      }
    }));
  }

  // 认证
  ws.send(JSON.stringify({
    type: 'auth',
    data: {
      apiKey,
      userKey
    }
  }));

  // 等待认证完成
  while (!isAuthenticated) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // 流式返回消息
  try {
    while (true) {
      if (abortController.signal.aborted) {
        break;
      }

      if (messageQueue.length > 0) {
        const message = messageQueue.shift();
        yield message;

        // 检查是否是结束消息
        if (message.type === 'result') {
          break;
        }
      } else {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }
  } finally {
    ws.close();
  }
}

function logDebug(message) {
  if (process.env.DEBUG) {
    console.debug(message);
  }
}
// 文件操作处理函数
function isFileOperation(toolName) {
  const fileTools = [
    'str-replace-editor',
    'view',
    'save-file',
    'remove-files',
    'launch-process',
    'read-process',
    'write-process',
    'kill-process',
    'list-processes'
  ];
  return fileTools.includes(toolName);
}

async function handleFileOperation(toolName, toolInput, cwd) {
  switch (toolName) {
    case 'view':
      return await handleViewOperation(toolInput, cwd);
    case 'str-replace-editor':
      return await handleEditOperation(toolInput, cwd);
    case 'save-file':
      return await handleSaveOperation(toolInput, cwd);
    case 'remove-files':
      return await handleRemoveOperation(toolInput, cwd);
    default:
      throw new Error(`Unsupported file operation: ${toolName}`);
  }
}

async function handleViewOperation(input, cwd) {
  const { path: filePath, view_range, search_query_regex } = input;
  const fullPath = join(cwd, filePath);

  try {
    const content = await fs.readFile(fullPath, 'utf8');
    const lines = content.split('\n');

    let result = content;

    if (view_range) {
      const [start, end] = view_range;
      const startIdx = Math.max(0, start - 1);
      const endIdx = end === -1 ? lines.length : Math.min(lines.length, end);
      result = lines.slice(startIdx, endIdx).join('\n');
    }

    if (search_query_regex) {
      const regex = new RegExp(search_query_regex, 'gi');
      const matchingLines = lines.filter(line => regex.test(line));
      result = matchingLines.join('\n');
    }

    return result;
  } catch (error) {
    throw new Error(`Failed to read file ${filePath}: ${error.message}`);
  }
}

async function handleEditOperation(input, cwd) {
  const { command, path: filePath, old_str, new_str, old_str_start_line_number, old_str_end_line_number } = input;
  const fullPath = join(cwd, filePath);

  if (command === 'str_replace') {
    try {
      const content = await fs.readFile(fullPath, 'utf8');
      const lines = content.split('\n');

      // 执行字符串替换
      const beforeLines = lines.slice(0, old_str_start_line_number - 1);
      const afterLines = lines.slice(old_str_end_line_number);
      const newLines = new_str.split('\n');

      const newContent = [...beforeLines, ...newLines, ...afterLines].join('\n');
      await fs.writeFile(fullPath, newContent, 'utf8');

      return `Successfully replaced content in ${filePath}`;
    } catch (error) {
      throw new Error(`Failed to edit file ${filePath}: ${error.message}`);
    }
  }

  throw new Error(`Unsupported edit command: ${command}`);
}

async function handleSaveOperation(input, cwd) {
  const { path: filePath, file_content } = input;
  const fullPath = join(cwd, filePath);

  try {
    // 确保目录存在
    const dir = join(fullPath, '..');
    await fs.mkdir(dir, { recursive: true });

    await fs.writeFile(fullPath, file_content, 'utf8');
    return `Successfully saved file ${filePath}`;
  } catch (error) {
    throw new Error(`Failed to save file ${filePath}: ${error.message}`);
  }
}

async function handleRemoveOperation(input, cwd) {
  const { file_paths } = input;
  const results = [];

  for (const filePath of file_paths) {
    try {
      const fullPath = join(cwd, filePath);
      await fs.unlink(fullPath);
      results.push(`Successfully removed ${filePath}`);
    } catch (error) {
      results.push(`Failed to remove ${filePath}: ${error.message}`);
    }
  }

  return results.join('\n');
}

async function handleOtherToolCall(toolName, toolInput, cwd) {
  // 处理其他非文件操作的工具调用
  throw new Error(`Tool ${toolName} not supported in remote mode`);
}

function isRunningWithBun() {
  return process.versions.bun !== undefined || process.env.BUN_INSTALL !== undefined;
}

class AbortError extends Error {
}
export {
  query,
  AbortError
};
