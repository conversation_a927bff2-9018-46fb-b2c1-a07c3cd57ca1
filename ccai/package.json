{"name": "@your-org/claude-code-client", "version": "1.0.0", "main": "sdk.mjs", "types": "sdk.d.ts", "bin": {"claude-client": "cli.js"}, "engines": {"node": ">=18.0.0"}, "type": "module", "author": "Your Name <<EMAIL>>", "license": "MIT", "description": "Remote client for Claude Code server. Connect to a shared Claude Code instance running on a remote server.", "homepage": "https://github.com/your-org/claude-code-client", "bugs": {"url": "https://github.com/your-org/claude-code-client/issues"}, "scripts": {"start": "node cli.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"ws": "^8.14.2"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}}